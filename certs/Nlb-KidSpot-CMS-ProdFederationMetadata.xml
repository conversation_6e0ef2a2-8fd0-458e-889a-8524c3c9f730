<?xml version="1.0" encoding="utf-8"?><EntityDescriptor ID="_c1870008-ac5b-4497-a0ae-0392c9d88e9f" entityID="https://sts.windows.net/0b11c524-9a1c-4e1b-84cb-6336aefc2243/" xmlns="urn:oasis:names:tc:SAML:2.0:metadata"><RoleDescriptor xsi:type="fed:SecurityTokenServiceType" protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706"><KeyDescriptor use="signing"><KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#"><X509Data><X509Certificate>MIIC8DCCAdigAwIBAgIQF0JvOfb09opAGzmZsqvr3TANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTAxMjcxMDEwMTNaFw0yNzAxMjcxMDEwMTBaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt9Zanx+VZIpLQF46K+KkHyN/R/MAaO2hJKn12RmNWDt5YyL58BGQ8iC3E9qPH6C5zK1iVsaq0pK27gvztwteThm/ta4Gh/Es4ACa7FnaswWk/uNBHaQF/ubarOctyEfKyeuBW+s1U2vV2Bv1cDKpqI3BaLVwy43lCoPZzgzUn0vH1I/kHcE15XaLGGG6/LXWpBEUSiBFl6+NZSL8Y0SdqYh2lw79V32aTYner/TYN1OR11z+PT//ipbF2tnvpfeM8txbvOjKZNO2mJI2rBiUZsFPU9ytTfqroRH4bXrWRzu6BlUqwr12srXvWJlegXJIaiEqhT6BLhE+D7juSEf4+QIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCIQR81mSIbYFj3mQJijO+JZ+EPEL4Y1TdQlJob/J/2NypAn/O6poSsa3RHWgbCHjqamKY4BvozPPhqUTALmoNNirC/cY2bmxjLnfgIwkVcxPzMBreA3G13+/aLnXQfaZjTYvOnEb4GQZGKBQGXFuVK72ZELkmG+GSXULLKpOVtS6nIJpK3NWfLf7haliK2z9H2VkWJ1gitjs+4A0YCN/0YZ8TTwz0Tc5brv06DBkrI5+XlEJlbACr8ivdhbG3LvKCZJSzh/m5uyeH1omcJdVn2cXz0K6oxgBebOSy2zs6MduRX/OdPciMZ+bcslibzkx9Vx9FdrXtFDA+wAsEZZkUq</X509Certificate></X509Data></KeyInfo></KeyDescriptor><fed:ClaimTypesOffered><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Name</auth:DisplayName><auth:Description>The mutable display name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Subject</auth:DisplayName><auth:Description>An immutable, globally unique, non-reusable identifier of the user that is unique to the application for which a token is issued.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Given Name</auth:DisplayName><auth:Description>First name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Surname</auth:DisplayName><auth:Description>Last name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/displayname" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Display Name</auth:DisplayName><auth:Description>Display name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/nickname" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Nick Name</auth:DisplayName><auth:Description>Nick name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationinstant" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Authentication Instant</auth:DisplayName><auth:Description>The time (UTC) when the user is authenticated to Windows Azure Active Directory.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationmethod" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Authentication Method</auth:DisplayName><auth:Description>The method that Windows Azure Active Directory uses to authenticate users.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/objectidentifier" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>ObjectIdentifier</auth:DisplayName><auth:Description>Primary identifier for the user in the directory. Immutable, globally unique, non-reusable.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/tenantid" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>TenantId</auth:DisplayName><auth:Description>Identifier for the user's tenant.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/identityprovider" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>IdentityProvider</auth:DisplayName><auth:Description>Identity provider for the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Email</auth:DisplayName><auth:Description>Email address of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/groups" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Groups</auth:DisplayName><auth:Description>Groups of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/accesstoken" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>External Access Token</auth:DisplayName><auth:Description>Access token issued by external identity provider.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/expiration" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>External Access Token Expiration</auth:DisplayName><auth:Description>UTC expiration time of access token issued by external identity provider.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/openid2_id" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>External OpenID 2.0 Identifier</auth:DisplayName><auth:Description>OpenID 2.0 identifier issued by external identity provider.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/claims/groups.link" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>GroupsOverageClaim</auth:DisplayName><auth:Description>Issued when number of user's group claims exceeds return limit.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/role" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Role Claim</auth:DisplayName><auth:Description>Roles that the user or Service Principal is attached to</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/wids" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>RoleTemplate Id Claim</auth:DisplayName><auth:Description>Role template id of the Built-in Directory Roles that the user is a member of</auth:Description></auth:ClaimType></fed:ClaimTypesOffered><fed:SecurityTokenServiceEndpoint><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/wsfed</wsa:Address></wsa:EndpointReference></fed:SecurityTokenServiceEndpoint><fed:PassiveRequestorEndpoint><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/wsfed</wsa:Address></wsa:EndpointReference></fed:PassiveRequestorEndpoint></RoleDescriptor><RoleDescriptor xsi:type="fed:ApplicationServiceType" protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706"><KeyDescriptor use="signing"><KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#"><X509Data><X509Certificate>MIIC8DCCAdigAwIBAgIQF0JvOfb09opAGzmZsqvr3TANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTAxMjcxMDEwMTNaFw0yNzAxMjcxMDEwMTBaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt9Zanx+VZIpLQF46K+KkHyN/R/MAaO2hJKn12RmNWDt5YyL58BGQ8iC3E9qPH6C5zK1iVsaq0pK27gvztwteThm/ta4Gh/Es4ACa7FnaswWk/uNBHaQF/ubarOctyEfKyeuBW+s1U2vV2Bv1cDKpqI3BaLVwy43lCoPZzgzUn0vH1I/kHcE15XaLGGG6/LXWpBEUSiBFl6+NZSL8Y0SdqYh2lw79V32aTYner/TYN1OR11z+PT//ipbF2tnvpfeM8txbvOjKZNO2mJI2rBiUZsFPU9ytTfqroRH4bXrWRzu6BlUqwr12srXvWJlegXJIaiEqhT6BLhE+D7juSEf4+QIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCIQR81mSIbYFj3mQJijO+JZ+EPEL4Y1TdQlJob/J/2NypAn/O6poSsa3RHWgbCHjqamKY4BvozPPhqUTALmoNNirC/cY2bmxjLnfgIwkVcxPzMBreA3G13+/aLnXQfaZjTYvOnEb4GQZGKBQGXFuVK72ZELkmG+GSXULLKpOVtS6nIJpK3NWfLf7haliK2z9H2VkWJ1gitjs+4A0YCN/0YZ8TTwz0Tc5brv06DBkrI5+XlEJlbACr8ivdhbG3LvKCZJSzh/m5uyeH1omcJdVn2cXz0K6oxgBebOSy2zs6MduRX/OdPciMZ+bcslibzkx9Vx9FdrXtFDA+wAsEZZkUq</X509Certificate></X509Data></KeyInfo></KeyDescriptor><fed:TargetScopes><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://sts.windows.net/0b11c524-9a1c-4e1b-84cb-6336aefc2243/</wsa:Address></wsa:EndpointReference></fed:TargetScopes><fed:ApplicationServiceEndpoint><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/wsfed</wsa:Address></wsa:EndpointReference></fed:ApplicationServiceEndpoint><fed:PassiveRequestorEndpoint><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/wsfed</wsa:Address></wsa:EndpointReference></fed:PassiveRequestorEndpoint></RoleDescriptor><IDPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol"><KeyDescriptor use="signing"><KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#"><X509Data><X509Certificate>MIIC8DCCAdigAwIBAgIQF0JvOfb09opAGzmZsqvr3TANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTAxMjcxMDEwMTNaFw0yNzAxMjcxMDEwMTBaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt9Zanx+VZIpLQF46K+KkHyN/R/MAaO2hJKn12RmNWDt5YyL58BGQ8iC3E9qPH6C5zK1iVsaq0pK27gvztwteThm/ta4Gh/Es4ACa7FnaswWk/uNBHaQF/ubarOctyEfKyeuBW+s1U2vV2Bv1cDKpqI3BaLVwy43lCoPZzgzUn0vH1I/kHcE15XaLGGG6/LXWpBEUSiBFl6+NZSL8Y0SdqYh2lw79V32aTYner/TYN1OR11z+PT//ipbF2tnvpfeM8txbvOjKZNO2mJI2rBiUZsFPU9ytTfqroRH4bXrWRzu6BlUqwr12srXvWJlegXJIaiEqhT6BLhE+D7juSEf4+QIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCIQR81mSIbYFj3mQJijO+JZ+EPEL4Y1TdQlJob/J/2NypAn/O6poSsa3RHWgbCHjqamKY4BvozPPhqUTALmoNNirC/cY2bmxjLnfgIwkVcxPzMBreA3G13+/aLnXQfaZjTYvOnEb4GQZGKBQGXFuVK72ZELkmG+GSXULLKpOVtS6nIJpK3NWfLf7haliK2z9H2VkWJ1gitjs+4A0YCN/0YZ8TTwz0Tc5brv06DBkrI5+XlEJlbACr8ivdhbG3LvKCZJSzh/m5uyeH1omcJdVn2cXz0K6oxgBebOSy2zs6MduRX/OdPciMZ+bcslibzkx9Vx9FdrXtFDA+wAsEZZkUq</X509Certificate></X509Data></KeyInfo></KeyDescriptor><SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/saml2" /><SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/saml2" /><SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/saml2" /></IDPSSODescriptor></EntityDescriptor>