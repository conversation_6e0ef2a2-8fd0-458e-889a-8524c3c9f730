// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

generator dbml {
  provider = "prisma-dbml-generator"
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model Application {
  id           String        @id
  name         String?
  apiKey       ApiKey?
  setting      Setting?
  interactions Interaction[]
  qrCodes      QrCode[]
  projections  Projection[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("applications")
}

model QrCode {
  id        String      @id @default(cuid())
  appId     String
  app       Application @relation(fields: [appId], references: [id])
  type      String
  code      String      @unique
  expiredAt DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([appId])
  @@map("qr_codes")
}

model Setting {
  id                               String      @id @default(cuid())
  appId                            String      @unique
  app                              Application @relation(fields: [appId], references: [id])
  backgroundId                     String?
  background                       Media?      @relation(fields: [backgroundId], references: [id])
  projectionLockTimeout            Int         @default(120)
  maxProjectionCharacterPerAccount Int         @default(3)
  minCharacters                    Int         @default(5)
  maxCharacters                    Int         @default(20)
  ttlCharacter                     Int         @default(15)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([backgroundId])
  @@map("settings")
}

model ApiKey {
  id    String      @id @default(cuid())
  key   String      @unique
  appId String      @unique
  app   Application @relation(fields: [appId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("api_keys")
}

model Interaction {
  id          String       @id @default(cuid())
  app         Application? @relation(fields: [appId], references: [id])
  appId       String?
  events      Event[]
  user        User?        @relation(fields: [userId], references: [id])
  userId      String?
  endedAt     DateTime?
  projections Projection[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([appId])
  @@index([userId])
  @@map("interactions")
}

model Event {
  id            String      @id @default(cuid())
  name          String
  interactionId String
  interaction   Interaction @relation(fields: [interactionId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([interactionId])
  @@map("events")
}

model Character {
  id             String           @id @default(cuid())
  name           String           @unique
  displayName    String?
  description    String?
  spawn          String           @default("random")
  movement       String           @default("random")
  size           Float            @default(1)
  speed          Float            @default(1)
  faceDirection  String           @default("left")
  price          Float            @default(0)
  attachments    Json             @default("{}")
  userCharacters UserCharacter[]
  characterColor CharacterColor[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("characters")
}

enum ColorType {
  REGULAR
  PREMIUM
}

model CharacterColor {
  type        ColorType @default(REGULAR)
  characterId String
  character   Character @relation(fields: [characterId], references: [id])
  colorId     String
  color       Color     @relation(fields: [colorId], references: [id])

  @@id([characterId, colorId])
  @@index([characterId])
  @@index([colorId])
}

model Color {
  id             String           @id @default(cuid())
  name           String           @unique
  hex            String           @default("#ffffff")
  characterColor CharacterColor[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  SUPERADMIN
  ADMIN
  USER
  ANONYMOUS
  EXTERNAL_USER
}

model User {
  id           String  @id @default(cuid())
  username     String  @unique
  password     String?
  displayName  String?
  role         Role    @default(ANONYMOUS)
  lastTokenIat Int?

  userCharacters UserCharacter[]
  sessions       Session[]
  transactions   Transaction[]
  interactions   Interaction[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  deactivatedAt DateTime?

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String   @unique
  user      User     @relation(fields: [userId], references: [id])
  token     String   @db.LongText
  expiredAt DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@map("sessions")
}

model Projection {
  id String @id @default(cuid())

  app                  Application?   @relation(fields: [appId], references: [id])
  appId                String?
  customizeAttachments Json?
  waiting              Boolean        @default(false)
  enteredAt            DateTime?
  exitedAt             DateTime?
  exiting              Boolean        @default(false)
  userCharacter        UserCharacter? @relation(fields: [userCharacterId], references: [id])
  userCharacterId      String?
  interaction          Interaction?   @relation(fields: [interactionId], references: [id])
  interactionId        String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userCharacterId])
  @@index([interactionId])
  @@index([appId])
  @@map("projections")
}

enum UserCharacterType {
  KIOSK
  WEBAPP
  PHYSICAL
}

model UserCharacter {
  id          String @id @default(cuid())
  userId      String
  characterId String

  attachments Json?
  user        User              @relation(fields: [userId], references: [id])
  character   Character         @relation(fields: [characterId], references: [id])
  type        UserCharacterType
  projections Projection[]
  source      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([characterId])
  @@map("user_characters")
}

model Transaction {
  id       String @id @default(cuid())
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  type     String
  metadata Json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@map("transactions")
}

model FAQ {
  id       String @id @default(cuid())
  question String
  answer   String
  order    Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("faqs")
}

enum MediaType {
  IMAGE
  VIDEO
}

model Media {
  id       String    @id @default(cuid())
  name     String
  type     MediaType
  path     String
  settings Setting[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("medias")
}
