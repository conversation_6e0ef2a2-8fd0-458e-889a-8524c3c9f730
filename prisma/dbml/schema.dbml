//// ------------------------------------------------------
//// THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
//// ------------------------------------------------------

Table applications {
  id String [pk]
  name String
  apiKey api_keys
  setting settings
  interactions interactions [not null]
  qrCodes qr_codes [not null]
  projections projections [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table qr_codes {
  id String [pk]
  appId String [not null]
  app applications [not null]
  type String [not null]
  code String [unique, not null]
  expiredAt DateTime [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table settings {
  id String [pk]
  appId String [unique, not null]
  app applications [not null]
  backgroundId String
  background medias
  projectionLockTimeout Int [not null, default: 120]
  maxProjectionCharacterPerAccount Int [not null, default: 3]
  minCharacters Int [not null, default: 5]
  maxCharacters Int [not null, default: 20]
  ttlCharacter Int [not null, default: 15]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table api_keys {
  id String [pk]
  key String [unique, not null]
  appId String [unique, not null]
  app applications [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table interactions {
  id String [pk]
  app applications
  appId String
  events events [not null]
  user users
  userId String
  endedAt DateTime
  projections projections [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table events {
  id String [pk]
  name String [not null]
  interactionId String [not null]
  interaction interactions [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table characters {
  id String [pk]
  name String [unique, not null]
  displayName String
  description String
  spawn String [not null, default: 'random']
  movement String [not null, default: 'random']
  size Float [not null, default: 1]
  speed Float [not null, default: 1]
  faceDirection String [not null, default: 'left']
  price Float [not null, default: 0]
  attachments Json [not null, default: '{}']
  userCharacters user_characters [not null]
  characterColor CharacterColor [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table CharacterColor {
  type ColorType [not null, default: 'REGULAR']
  characterId String [not null]
  character characters [not null]
  colorId String [not null]
  color Color [not null]

  indexes {
    (characterId, colorId) [pk]
  }
}

Table Color {
  id String [pk]
  name String [unique, not null]
  hex String [not null, default: '#ffffff']
  characterColor CharacterColor [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table users {
  id String [pk]
  username String [unique, not null]
  password String
  displayName String
  role Role [not null, default: 'ANONYMOUS']
  lastTokenIat Int
  userCharacters user_characters [not null]
  sessions sessions [not null]
  transactions transactions [not null]
  interactions interactions [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
  deactivatedAt DateTime
}

Table sessions {
  id String [pk]
  userId String [unique, not null]
  user users [not null]
  token String [not null]
  expiredAt DateTime [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table projections {
  id String [pk]
  app applications
  appId String
  customizeAttachments Json
  waiting Boolean [not null, default: false]
  enteredAt DateTime
  exitedAt DateTime
  exiting Boolean [not null, default: false]
  userCharacter user_characters
  userCharacterId String
  interaction interactions
  interactionId String
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table user_characters {
  id String [pk]
  userId String [not null]
  characterId String [not null]
  attachments Json
  user users [not null]
  character characters [not null]
  type UserCharacterType [not null]
  projections projections [not null]
  source String
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table transactions {
  id String [pk]
  userId String [not null]
  user users [not null]
  type String [not null]
  metadata Json [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table faqs {
  id String [pk]
  question String [not null]
  answer String [not null]
  order Int [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table medias {
  id String [pk]
  name String [not null]
  type MediaType [not null]
  path String [not null]
  settings settings [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Enum ColorType {
  REGULAR
  PREMIUM
}

Enum Role {
  SUPERADMIN
  ADMIN
  USER
  ANONYMOUS
  EXTERNAL_USER
}

Enum UserCharacterType {
  KIOSK
  WEBAPP
  PHYSICAL
}

Enum MediaType {
  IMAGE
  VIDEO
}

Ref: qr_codes.appId > applications.id

Ref: settings.appId - applications.id

Ref: settings.backgroundId > medias.id

Ref: api_keys.appId - applications.id

Ref: interactions.appId > applications.id

Ref: interactions.userId > users.id

Ref: events.interactionId > interactions.id

Ref: CharacterColor.characterId > characters.id

Ref: CharacterColor.colorId > Color.id

Ref: sessions.userId > users.id

Ref: projections.appId > applications.id

Ref: projections.userCharacterId > user_characters.id

Ref: projections.interactionId > interactions.id

Ref: user_characters.userId > users.id

Ref: user_characters.characterId > characters.id

Ref: transactions.userId > users.id