import { PrismaClient, Role } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

const seedSuperAdmin = async () => {
  await prisma.user.upsert({
    where: {
      username: 'superadmin',
    },
    update: {},
    create: {
      username: 'superadmin',
      password: bcrypt.hashSync('', 12),
      role: Role.SUPERADMIN,
    },
  });

  console.log(`Super Admin Created`);
};

const seedCMSAdmin = async () => {
  await prisma.user.upsert({
    where: {
      username: 'cmsadmin',
    },
    update: {},
    create: {
      username: 'cmsadmin',
      password: bcrypt.hashSync('', 12),
      role: Role.ADMIN,
    },
  });

  console.log(`CMS Admin Created`);
};

const seedCharacter = async () => {
  await prisma.color.createMany({
    data: [
      { name: 'blue' },
      { name: 'gray' },
      { name: 'green' },
      { name: 'orange' },
      { name: 'red' },
      { name: 'violet' },
      { name: 'white' },
      { name: 'yellow' },
    ],
    skipDuplicates: true,
  });

  await prisma.character.createMany({
    data: [
      {
        name: 'xueyu',
        description: 'Beautiful XueYu turn Book Bugs into snowflakes!',
        attachments: {
          hand: 'hand_blue',
          head: 'head_blue',
          l_leg: 'l_leg_blue',
          r_leg: 'r_leg_blue',
          staff: 'staff_blue',
          l_wing: 'l_wing_blue',
          r_wing: 'r_wing_blue',
          upper_body: 'upper_body_blue',
          l_lower_arm: 'l_lower_arm_blue',
          l_upper_arm: 'l_upper_arm_blue',
          r_lower_arm: 'r_lower_arm_blue',
          lower_body_over: 'lower_body_over_blue',
          lower_body_under: 'lower_body_under_blue',
        },
        spawn: 'random',
        movement: 'random',
        size: 1,
        speed: 1,
        faceDirection: 'left',
        price: 0,
      },
      {
        name: 'drafrost',
        description: 'His fearsome appearance belies a gentle heart.',
        attachments: {
          back_leg: 'back_leg_blue',
          body: 'body_blue',
          frontleg1: 'frontleg1_blue',
          frontlegs2: 'frontlegs2_blue',
          head: 'head_blue',
          l_leg2: 'l_leg2_blue',
          tail: 'tail_blue',
          wings: 'wings_blue',
        },
        spawn: 'random',
        movement: 'random',
        size: 1,
        speed: 1,
        faceDirection: 'left',
        price: 0,
      },
      {
        name: 'wintrix',
        description: 'Snowball fights occur when Wintrix is nearby.',
        attachments: {
          body: 'body_blue',
          head: 'head_blue',
          l_arm1: 'l_arm1_blue',
          l_arm2: 'l_arm2_blue',
          l_leg: 'l_leg_blue',
          r_arm1: 'r_arm1_blue',
          r_arm2: 'r_arm2_blue',
          r_leg: 'r_leg_blue',
        },
        spawn: 'random',
        movement: 'random',
        size: 1,
        speed: 1,
        faceDirection: 'left',
        price: 0,
      },
      {
        name: 'nuun',
        description: 'Harmless as a snowball travelling at 80 km/hour.',
        attachments: {
          body: 'body_blue',
          head: 'head_blue',
          l_arm_lower: 'l_arm_lower_blue',
          l_arm_upper: 'l_arm_upper_blue',
          l_leg: 'l_leg_blue',
          r_arm_lower: 'r_arm_lower_blue',
          r_arm_upper: 'r_arm_upper_blue',
          r_leg: 'r_leg_blue',
          scarf: 'scarf_blue',
          snow_effect: 'snow_effect_blue',
        },
        spawn: 'random',
        movement: 'random',
        size: 1,
        speed: 1,
        faceDirection: 'right',
        price: 0,
      },
      {
        name: 'kaka',
        description: 'Prefers his birthday cake: fish and fruits in ice!',
        attachments: {
          body: 'body_blue',
          head: 'head_blue',
          l_arm1: 'l_arm1_blue',
          l_arm2: 'l_arm2_blue',
          l_foot: 'l_foot_blue',
          r_arm1: 'r_arm1_blue',
          r_arm2: 'r_arm2_blue',
          r_foot: 'r_foot_blue',
          r_arm1_fingers: 'r_arm1_fingers_blue',
        },
        spawn: 'ground',
        movement: 'ground',
        size: 1,
        speed: 1,
        faceDirection: 'left',
        price: 0,
      },
      {
        name: 'ix',
        description: 'Scuttles behind Aurair and does all the paperwork',
        attachments: {
          bag: 'bag_blue',
          hat: 'hat_blue',
          body: 'body_blue',
          wand: 'wand_blue',
          leftleg: 'leftleg_blue',
          lefthand: 'lefthand_blue',
          rightleg: 'rightleg_blue',
          righthand: 'righthand_blue',
        },
        spawn: 'ground',
        movement: 'ground',
        size: 1,
        speed: 1,
        faceDirection: 'left',
        price: 0,
      },
      {
        name: 'sciphy',
        description: 'Daring alchemist Sciphy is a potions prodigy.',
        attachments: {
          coat: 'coat_blue',
          head: 'head_blue',
          left: 'left_blue',
          right: 'right_blue',
          bottom: 'bottom_blue',
        },
        spawn: 'random',
        movement: 'random',
        size: 1,
        speed: 1,
        faceDirection: 'left',
        price: 0,
      },
      {
        name: 'kroos',
        description: 'A radiobiology intern on the Thunderbug spaceship.',
        attachments: {
          cup: 'cup_blue',
          body: 'body_blue',
          head: 'head_blue',
          shell: 'shell_blue',
          backleg: 'backleg_blue',
          leftarm1: 'leftarm1_blue',
          leftarm2: 'leftarm2_blue',
          rightarm: 'rightarm_blue',
        },
        spawn: 'random',
        movement: 'random',
        size: 1,
        speed: 1,
        faceDirection: 'left',
        price: 0,
      },
      {
        name: 'betelgeuse',
        description: 'Watch out! A prankster through and through!',
        attachments: {
          body: 'body_blue',
          bodytop: 'bodytop_blue',
          head: 'head_blue',
          leftarm1: 'leftarm1_blue',
          leftleg: 'leftleg_blue',
          rightarm1: 'rightarm1_blue',
          cane: 'cane_blue',
          lefttarm2: 'lefttarm2_blue',
          rightarm2: 'rightarm2_blue',
          rightleg: 'rightleg_blue',
        },
        spawn: 'random',
        movement: 'random',
        size: 1,
        speed: 1,
        faceDirection: 'left',
        price: 0,
      },
      {
        name: 'kavi',
        description: 'Poet Kavi depicts beauty through his verses.',
        attachments: {
          bag: 'bag_blue',
          body: 'body_blue',
          head: 'head_blue',
          rock: 'rock_blue',
          brushtop: 'brushtop_blue',
          leftarm1: 'leftarm1_blue',
          leftarm2: 'leftarm2_blue',
          leftwing: 'leftwing_blue',
          rightarm: 'rightarm_blue',
          rightwing: 'rightwing_blue',
        },
        spawn: 'ground',
        movement: 'ground',
        size: 1,
        speed: 1,
        faceDirection: 'left',
        price: 20,
      },
      {
        name: 'buwan',
        description: 'Buwan has grand dreams that go unfulfilled.',
        attachments: {
          body: 'body_blue',
          star1: 'star1_blue',
          star2: 'star2_blue',
          star3: 'star3_blue',
          star4: 'star4_blue',
          helmet: 'helmet_blue',
          bubble1: 'bubble1_blue',
          bubble2: 'bubble2_blue',
          bubble3: 'bubble3_blue',
          bubble4: 'bubble4_blue',
          bubble5: 'bubble5_blue',
          bubble6: 'bubble6_blue',
          bubble7: 'bubble7_blue',
          bubble8: 'bubble8_blue',
          backpack: 'backpack_blue',
        },
        spawn: 'random',
        movement: 'random',
        size: 1,
        speed: 1,
        faceDirection: 'right',
        price: 20,
      },
      {
        name: 'cyane',
        description: `Alien Cyane's skin glimmers in shades of blue!`,
        attachments: {
          ufo: 'ufo_blue',
          body: 'body_blue',
          glow: 'glow_blue',
          head: 'head_blue',
          tail: 'tail_blue',
          leftleg: 'leftleg_blue',
          leftarm1: 'leftarm1_blue',
          leftarm2: 'leftarm2_blue',
          rightleg: 'rightleg_blue',
          leftwing1: 'leftwing1_blue',
          leftwing2: 'leftwing2_blue',
          rightwing1: 'rightwing1_blue',
          rightwing2: 'rightwing2_blue',
        },
        spawn: 'random',
        movement: 'random',
        size: 1,
        speed: 1,
        faceDirection: 'right',
        price: 20,
      },
      {
        name: 'xora',
        description: `Xora's mind, she is destined for true greatness.`,
        attachments: {
          body: 'body_blue',
          glow: 'glow_blue',
          head: 'head_blue',
          tail: 'tail_blue',
          star1: 'star1_blue',
          star2: 'star2_blue',
          star3: 'star3_blue',
          star4: 'star4_blue',
          star5: 'star5_blue',
          bagback: 'bagback_blue',
          leftleg: 'leftleg_blue',
          bagfront: 'bagfront_blue',
          leftarm1: 'leftarm1_blue',
          leftarm2: 'leftarm2_blue',
          rightleg: 'rightleg_blue',
          rightarm1: 'rightarm1_blue',
          rightarm2: 'rightarm2_blue',
          rightwrist: 'rightwrist_blue',
        },
        spawn: 'ground',
        movement: 'ground',
        size: 1,
        speed: 1,
        faceDirection: 'right',
        price: 20,
      },
    ],
    skipDuplicates: true,
  });

  const colors = await prisma.color.findMany();
  const characters = await prisma.character.findMany();

  for (const character of characters) {
    for (const color of colors) {
      await prisma.characterColor.upsert({
        where: {
          characterId_colorId: {
            characterId: character.id,
            colorId: color.id,
          },
        },
        update: {},
        create: {
          characterId: character.id,
          colorId: color.id,
        },
      });
    }
  }
};

const seedFaq = async () => {
  await prisma.fAQ.createMany({
    data: [
      {
        order: 0,
        question: 'What is the game about?',
        answer:
          'The game is about a character who has to jump over obstacles and collect coins to earn points. The character has to avoid obstacles to stay alive.',
      },
      {
        order: 1,
        question: 'How to play the game?',
        answer:
          'The character will jump automatically. The player has to tap on the screen to make the character jump higher. The player has to avoid obstacles and collect coins to earn points.',
      },
      {
        order: 2,
        question: 'How to earn points?',
        answer:
          'The player has to collect coins to earn points. The player will earn 1 point for each coin collected.',
      },
      {
        order: 3,
        question: 'What happens when the character hits an obstacle?',
        answer:
          'The character will die if it hits an obstacle. The game will end, and the player will have to start over.',
      },
      {
        order: 4,
        question: 'How to revive the character?',
        answer:
          'The player can revive the character by watching a video ad. The player will get a chance to continue the game from where the character died.',
      },
      {
        order: 5,
        question: 'How to buy new characters?',
        answer:
          'The player can buy new characters using coins. The player can also buy characters using real money.',
      },
      {
        order: 6,
        question: 'How to unlock new levels?',
        answer:
          'The player can unlock new levels by earning points. The player has to reach a certain number of points to unlock a new level.',
      },
      {
        order: 7,
        question: 'How to change the character’s appearance?',
        answer:
          'The player can change the character’s appearance by buying new skins. The player can also unlock new skins by reaching a certain number of points.',
      },
      {
        order: 8,
        question: 'How to change the game settings?',
        answer:
          'The player can change the game settings by tapping on the settings icon. The player can change the sound, music, and other settings from the settings menu.',
      },
      {
        order: 9,
        question: 'How to contact customer support?',
        answer:
          'The player can contact customer support by tapping on the support icon. The player can send an email to customer support from the support menu.',
      },
      {
        order: 10,
        question: 'How to report a bug?',
        answer:
          'The player can report a bug by tapping on the bug icon. The player can send a bug report to the development team from the bug menu.',
      },
      {
        order: 11,
        question: 'How to change the language?',
        answer:
          'The player can change the language by tapping on the language icon. The player can select a language from the language menu.',
      },
      {
        order: 12,
        question: 'How to change the game mode?',
        answer:
          'The player can change the game mode by tapping on the game mode icon. The player can select a game mode from the game mode menu.',
      },
      {
        order: 13,
        question: 'How to change the game difficulty?',
        answer:
          'The player can change the game difficulty by tapping on the game difficulty icon. The player can select a game difficulty from the game difficulty menu.',
      },
      {
        order: 14,
        question: 'How to change the game theme?',
        answer:
          'The player can change the game theme by tapping on the game theme icon. The player can select a game theme from the game theme menu.',
      },
      {
        order: 15,
        question: 'How to change the game music?',
        answer:
          'The player can change the game music by tapping on the game music icon. The player can select a game music from the game music menu.',
      },
      {
        order: 16,
        question: 'How to change the game sound?',
        answer:
          'The player can change the game sound by tapping on the game sound icon. The player can select a game sound from the game sound menu.',
      },
      {
        order: 17,
        question: 'How to change the game controls?',
        answer:
          'The player can change the game controls by tapping on the game controls icon. The player can select a game control from the game controls menu.',
      },
      {
        order: 18,
        question: 'How to change the game graphics?',
        answer:
          'The player can change the game graphics by tapping on the game graphics icon. The player can select a game graphic from the game graphics menu.',
      },
      {
        order: 19,
        question: 'How to change the game resolution?',
        answer:
          'The player can change the game resolution by tapping on the game resolution icon. The player can select a game resolution from the game resolution menu.',
      },
      {
        order: 20,
        question: 'How to change the game frame rate?',
        answer:
          'The player can change the game frame rate by tapping on the game frame rate icon. The player can select a game frame rate from the game frame rate menu.',
      },
      {
        order: 21,
        question: 'How to change the game quality?',
        answer:
          'The player can change the game quality by tapping on the game quality icon. The player can select a game quality from the game quality menu.',
      },
      {
        order: 22,
        question: 'How to change the game speed?',
        answer:
          'The player can change the game speed by tapping on the game speed icon. The player can select a game speed from the game speed menu.',
      },
      {
        order: 23,
        question: 'How to change the game size?',
        answer:
          'The player can change the game size by tapping on the game size icon. The player can select a game size from the game size menu.',
      },
      {
        order: 24,
        question: 'How to change the game screen ratio?',
        answer:
          'The player can change the game screen ratio by tapping on the game screen ratio icon. The player can select a game screen ratio from the game screen ratio menu.',
      },
      {
        order: 25,
        question: 'How to change the game screen orientation?',
        answer:
          'The player can change the game screen orientation by tapping on the game screen orientation icon. The player can select a game screen orientation from the game screen orientation menu.',
      },
    ],
  });
};

async function main() {
  // Create SuperAdmin
  await seedCMSAdmin();
  await seedSuperAdmin();
  await seedCharacter();
  await seedFaq();
}
main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
