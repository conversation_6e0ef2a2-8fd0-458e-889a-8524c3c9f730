# SAML Authentication Setup

This document describes how to set up and use SAML authentication with Microsoft Entra ID (Azure AD).

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```env
# SAML Configuration
SAML_ENTITY_ID="nlb-kidspot-cms"
SAML_CALLBACK_URL="https://your-domain.com/auth/saml/callback"
SAML_IDP_SSO_URL="https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/saml2"
SAML_IDP_ENTITY_ID="https://sts.windows.net/0b11c524-9a1c-4e1b-84cb-6336aefc2243/"
```

### Certificate

The SAML certificate is stored in `certs/Nlb-KidSpot-CMS-Prod.cer` and is automatically loaded by the SAML strategy.

## API Endpoints

### Initiate SAML Login

```
GET /auth/saml/login
```

This endpoint redirects the user to the Microsoft Entra ID login page.

### SAML Callback

```
POST /auth/saml/callback
```

This endpoint receives the SAML response from Microsoft Entra ID and returns a JWT token.

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## SAML Flow

1. User visits `/auth/saml/login`
2. Application redirects user to Microsoft Entra ID
3. User authenticates with Microsoft
4. Microsoft posts SAML response to `/auth/saml/callback`
5. Application validates SAML response and creates/updates user
6. Application returns JWT token

## User Management

- SAML users are created with `EXTERNAL_USER` role
- Users are identified by the SAML `nameID` attribute
- User sessions are stored in the database with 24-hour expiration
- Deactivated users cannot authenticate

## Security Features

- SAML response signature validation using the provided certificate
- SHA-256 signature and digest algorithms
- Protection against replay attacks
- Proper SAML assertion validation

## Microsoft Entra ID Configuration

The Identity Provider is configured with:
- **Entity ID:** `https://sts.windows.net/0b11c524-9a1c-4e1b-84cb-6336aefc2243/`
- **SSO URL:** `https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/saml2`
- **Certificate:** Embedded in federation metadata

## Troubleshooting

1. **Certificate Issues:** Ensure the certificate in `certs/Nlb-KidSpot-CMS-Prod.cer` is valid and matches the one in the federation metadata.

2. **Callback URL:** Make sure the `SAML_CALLBACK_URL` environment variable matches the URL configured in Microsoft Entra ID.

3. **Entity ID:** Verify that the `SAML_ENTITY_ID` matches the identifier configured in Microsoft Entra ID.

4. **Logs:** Check application logs for SAML validation errors and authentication events.
