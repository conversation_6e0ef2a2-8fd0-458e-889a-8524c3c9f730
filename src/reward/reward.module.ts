import { Module } from '@nestjs/common';
import { ApplicationModule } from 'src/application/application.module';
import { AuthModule } from 'src/auth/auth.module';
import { RewardController } from './reward.controller';
import { RewardService } from './reward.service';

@Module({
  imports: [ApplicationModule, AuthModule],
  controllers: [RewardController],
  providers: [RewardService],
  exports: [RewardService],
})
export class RewardModule {}
