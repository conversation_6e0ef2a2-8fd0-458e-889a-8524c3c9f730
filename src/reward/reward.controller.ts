import {
  Controller,
  Get,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { UserFromJwt } from 'src/auth/auth.type';
import { User } from 'src/auth/decorators/params.decorator';
import { Roles } from 'src/auth/decorators/role.decorator';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { RewardService } from './reward.service';

@ApiTags('Reward')
@Controller('reward')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class RewardController {
  constructor(private readonly rewardService: RewardService) {}

  @Get('points')
  @Roles('EXTERNAL_USER')
  getPoints(@User() user: UserFromJwt) {
    return this.rewardService.getPoints(user.pdvId);
  }

  @Get('points/history')
  @Roles('EXTERNAL_USER')
  @ApiQuery({ name: 'page', example: 1, required: true })
  @ApiQuery({ name: 'limit', example: 10, required: true })
  getPointsHistory(
    @User() user: UserFromJwt,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
  ) {
    return this.rewardService.getPointsHistory(user, { page, limit });
  }
}
