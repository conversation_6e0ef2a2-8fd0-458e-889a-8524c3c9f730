import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { CustomPrismaService } from 'nestjs-prisma';
import { PageNumberPaginationOptions } from 'prisma-extension-pagination';
import { ApplicationService } from 'src/application/application.service';
import { UserFromJwt } from 'src/auth/auth.type';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { RedemptionDto } from './dto/redemption.dto';
import { TransactionType } from './reward.types';

@Injectable()
export class RewardService {
  private readonly EXTERNAL_REWARD_API = process.env.REWARD_API_URL;
  private readonly EXTERNAL_REWARD_APP_BASIC_AUTH =
    process.env.REWARD_APP_BASIC_AUTH;

  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    private readonly applicationService: ApplicationService,
  ) { }

  async getPoints(pdvId: number) {
    const url = `${this.EXTERNAL_REWARD_API}/points/${pdvId}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Authorization: `Basic ${this.EXTERNAL_REWARD_APP_BASIC_AUTH}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.status !== 200) {
      throw new InternalServerErrorException('Failed to get points');
    }

    const data = (await response.json()) as { availablePoints: number };

    return data;
  }

  async getPointsHistory(
    { sub: userId }: UserFromJwt,
    paginateParams: PageNumberPaginationOptions,
  ) {
    return this.prisma.client.transaction
      .paginate({
        where: {
          userId,
          type: TransactionType.POINTS_REDEMPTION,
        },
        orderBy: {
          createdAt: 'desc',
        },
      })
      .withPages({
        ...paginateParams,
        includePageCount: true,
      });
  }

  async redemption({ pdvId }: UserFromJwt, { points }: RedemptionDto) {
    const url = `${this.EXTERNAL_REWARD_API}/points/${pdvId}/redemptions`;

    // if (points % 20 !== 0) {
    //   throw new InternalServerErrorException('Points must be divisible by 20');
    // }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Basic ${this.EXTERNAL_REWARD_APP_BASIC_AUTH}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        configCode: 'CHILDRENGAMI',
        catalogueCode: 'AVATAR1',
        redemptionCount: points / 20,
      }),
    });
    // console.log({
    //   url,
    //   auth: `Basic ${this.EXTERNAL_REWARD_APP_BASIC_AUTH}`,
    //   body: { configCode: 'CHILDRENGAMI',
    //     catalogueCode: 'AVATAR1',
    //     redemptionCount: points / 20,},
    //     response
    // })

    if (response.status !== 200) {
      throw new InternalServerErrorException('Failed to redeem points');
    }

    const data = (await response.json()) as { availablePoints: number };

    return data;
  }
}
