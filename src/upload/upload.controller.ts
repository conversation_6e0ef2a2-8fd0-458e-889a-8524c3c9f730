import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseFilePipeBuilder,
  ParseIntPipe,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { MediaType } from '@prisma/client';
import { memoryStorage } from 'multer';
import { Roles } from 'src/auth/decorators/role.decorator';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { UploadDto } from './dto/upload.dto';
import { UploadService } from './upload.service';

@ApiTags('Upload')
@Controller('upload')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Roles('ADMIN')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'multipart/form-data',
    required: true,
    schema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
        },
        name: {
          type: 'string',
        },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @Post()
  @UseInterceptors(FileInterceptor('file', { storage: memoryStorage() }))
  async uploadFile(
    @Body() payload: UploadDto,
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /(jpg|jpeg|png|gif|mp4)/,
        })
        .addMaxSizeValidator({ maxSize: 1024 * 1024 * 100 }) // Limit to 100MB
        .build({ errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY }),
    )
    file: Express.Multer.File,
  ) {
    if (file.originalname.split('.').length > 2) {
      throw new BadRequestException('Invalid file name');
    }

    return await this.uploadService.handleUploadFile(payload, file);
  }

  @Roles('ADMIN')
  @Get()
  @ApiQuery({ name: 'page', example: 1, required: true })
  @ApiQuery({ name: 'limit', example: 10, required: true })
  @ApiQuery({ name: 'type', example: 'VIDEO,IMAGE', required: false })
  findAll(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
    @Query('type') type: string,
  ) {
    return this.uploadService.paginate(
      { page, limit },
      {
        type: type ? (type.split(',') as MediaType[]) : undefined,
      },
    );
  }

  @Roles('ADMIN')
  @Delete(':id')
  removeFile(@Param('id') id: string) {
    return this.uploadService.removeFile(id);
  }
}
