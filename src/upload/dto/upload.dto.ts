import { ApiBody, ApiProperty } from '@nestjs/swagger';
import { MediaType } from '@prisma/client';
import { IsEnum, IsString } from 'class-validator';

export class UploadDto {
  @ApiProperty({
    description: 'File Name',
    example: 'spine',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'File type',
    example: MediaType.VIDEO,
  })
  @IsEnum(MediaType)
  type: MediaType;
}

export const ApiMultiFile =
  (fileName: string = 'files'): MethodDecorator =>
  (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    ApiBody({
      type: 'multipart/form-data',
      required: true,
      schema: {
        type: 'object',
        properties: {
          type: {
            type: 'string',
          },
          path: {
            type: 'string',
          },
          [fileName]: {
            type: 'array',
            items: {
              type: 'string',
              format: 'binary',
            },
          },
        },
      },
    })(target, propertyKey, descriptor);
  };
