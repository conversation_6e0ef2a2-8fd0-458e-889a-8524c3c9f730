import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
} from '@nestjs/common';
import { MediaType } from '@prisma/client';
import { createWriteStream, existsSync, mkdirSync, unlinkSync } from 'fs';
import { CustomPrismaService } from 'nestjs-prisma';
import { extname, join } from 'path';
import { PageNumberPaginationOptions } from 'prisma-extension-pagination';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { UploadDto } from './dto/upload.dto';
import * as NodeClam from 'clamscan';

@Injectable()
export class UploadService {
  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
  ) {}

  public async handleUploadFile(
    { name, type }: UploadDto,
    file: Express.Multer.File,
  ) {
    const path = type === MediaType.IMAGE ? 'images' : 'videos';

    if (!existsSync(join(process.cwd(), 'public', 'upload'))) {
      mkdirSync(join(process.cwd(), 'public', 'upload'));
    }

    const storagePath = join(process.cwd(), 'public', 'upload', path);

    if (!existsSync(storagePath)) {
      mkdirSync(storagePath);
    }

    const fileExt = extname(file.originalname);
    const fileName = `${crypto.randomUUID()}${fileExt}`;
    const writeStream = createWriteStream(join(storagePath, fileName));
    writeStream.write(file.buffer);

    const clamscan = await new NodeClam().init({});

    const version = await clamscan.getVersion();
    console.log(`ClamAV Version: ${version}`);

    const {
      isInfected,
      file: scannedFile,
      viruses,
    } = await clamscan.isInfected(join(storagePath, fileName));

    if (isInfected) {
      console.log(`${scannedFile} is infected with ${viruses}!`);
      unlinkSync(join(storagePath, fileName));
      throw new BadRequestException('File is infected');
    } else {
      console.log(`${scannedFile} is OK!`);
    }

    const newMedia = await this.prisma.client.media.create({
      data: {
        name,
        type,
        path: `${process.env.SERVER_URL}/public/upload/${path}/${fileName}`,
      },
    });

    return newMedia;
  }

  public async paginate(
    paginateParams: PageNumberPaginationOptions,
    filter: {
      type?: MediaType[];
    },
  ) {
    return await this.prisma.client.media
      .paginate({
        where: { type: { in: filter.type } },
        orderBy: { createdAt: 'desc' },
      })
      .withPages({
        ...paginateParams,
        includePageCount: true,
      });
  }

  public async removeFile(id: string) {
    const media = await this.prisma.client.media.findUniqueOrThrow({
      where: { id },
    });

    const path = media.path.replace(process.env.SERVER_URL, '');

    const filePath = join(process.cwd(), path);

    if (!existsSync(filePath)) {
      return false;
    }

    unlinkSync(filePath);
    await this.prisma.client.media.delete({ where: { id } });
    return true;
  }
}
