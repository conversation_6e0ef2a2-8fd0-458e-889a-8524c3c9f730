import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class CreateInteractionDto {
  @ApiPropertyOptional({
    description: 'App id of the interaction',
    example: '1',
  })
  @IsString()
  @IsOptional()
  appId?: string;

  @ApiPropertyOptional({
    description: 'The id of the user (only for web app)',
    example: '1',
  })
  @IsString()
  @IsOptional()
  userId?: string;
}
