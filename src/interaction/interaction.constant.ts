export const EVENT_MESSAGES = {
  // Kiosk
  SELECTION_SCREEN_MSG: 'Selection Screen Visited',
  CUSTOMIZATION_SCREEN_VISIT_MSG: 'Customization Screen Visited',
  CHARACTER_SUBMITTED_MSG: 'Character Submitted',
  EVENT_NOT_FOUND_ERR: 'Event not found',
  CHARACTER_CUSTOMIZATION_START_MSG: 'Character Customization Start',
  CHARACTER_CUSTOMIZATION_END_MSG: 'Character Customization End',
  RE_CHARACTER_SELECTION_MSG: 'RE- Selection of Character',
  IDLE_SCREEN_MSG: 'Your are idle for to long!',

  // Web App
  USER_SHARE_CHARACTER_MSG: 'User Shared Character',
  USER_SCAN_LIBRARY_QR_MSG: 'User Scanned Library QR',
};
