import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { CustomPrismaService } from 'nestjs-prisma';
import { CreateEventDto } from 'src/interaction/dto/create-event.dto';
import { CreateInteractionDto } from 'src/interaction/dto/create-interaction.dto';
import { EndInteractionDto } from 'src/interaction/dto/end-interaction.dto';
import { dateWithTz } from 'src/libs/datetime';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { ConnectAppIdDto } from './dto/connect-app-id.dto';

@Injectable()
export class InteractionService {
  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
  ) {}

  async createInteraction({ appId, userId }: CreateInteractionDto) {
    if (userId) {
      const recentInteractions = await this.prisma.client.interaction.findMany({
        where: { userId, endedAt: null },
      });

      if (recentInteractions.length) {
        for await (const ri of recentInteractions) {
          await this.prisma.client.interaction.update({
            where: { id: ri.id },
            data: { endedAt: dateWithTz(ri.createdAt).endOf('day').toDate() },
          });
        }
      }
    }

    const newInteraction = await this.prisma.client.interaction.create({
      data: {},
    });

    if (appId) {
      await this.prisma.client.interaction.update({
        where: { id: newInteraction.id },
        data: { app: { connect: { id: appId } } },
      });
    }

    if (userId) {
      await this.prisma.client.interaction.update({
        where: { id: newInteraction.id },
        data: { user: { connect: { id: userId } } },
      });
    }

    return await this.prisma.client.interaction.findUnique({
      where: { id: newInteraction.id },
    });
  }

  async connectWithAppId(appId: string, { interactionId }: ConnectAppIdDto) {
    return await this.prisma.client.interaction.update({
      where: { id: interactionId },
      data: { app: { connect: { id: appId } } },
    });
  }

  async endInteraction({ interactionId }: EndInteractionDto) {
    return await this.prisma.client.interaction.update({
      where: { id: interactionId },
      data: { endedAt: new Date() },
    });
  }

  async createEvent(createEventDto: CreateEventDto) {
    const checked = await this.isInteractionValid(createEventDto.interactionId);

    if (typeof checked === 'string') {
      return checked;
    }

    if (createEventDto.name.includes('[CTPLKIOSK')) {
      await this.prisma.client.interaction.update({
        where: {
          id: createEventDto.interactionId,
        },
        data: {
          user: {
            connect: {
              username: 'anonymous',
            },
          },
        },
      });
    }

    return await this.prisma.client.event.create({
      data: {
        name: createEventDto.name,
        interactionId: createEventDto.interactionId,
      },
    });
  }

  async isInteractionValid(interactionId: string) {
    const interaction = await this.prisma.client.interaction.findUnique({
      where: { id: interactionId },
    });

    if (!interaction) {
      throw new BadRequestException('Invalid interaction id');
    }

    if (interaction.endedAt) {
      throw new BadRequestException('Interaction already ended');
    }

    return interaction;
  }
}
