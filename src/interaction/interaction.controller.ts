import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExcludeEndpoint,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { ApiKeyGuard } from 'src/auth/guards/apikey.guard';
import { EndInteractionDto } from 'src/interaction/dto/end-interaction.dto';
import { AppId } from 'src/libs/decorators/app_id.decorator';
import { CreateEventDto } from './dto/create-event.dto';
import { InteractionService } from './interaction.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { Roles } from 'src/auth/decorators/role.decorator';
import { User } from 'src/auth/decorators/params.decorator';
import { UserFromJwt } from 'src/auth/auth.type';
import { ConnectAppIdDto } from './dto/connect-app-id.dto';

@ApiTags('Interaction')
@Controller('interaction')
export class InteractionController {
  constructor(private readonly interactionService: InteractionService) {}

  @Post('/start')
  @ApiSecurity('x-api-key')
  @UseGuards(ApiKeyGuard)
  createInteraction(@AppId() appId: string) {
    return this.interactionService.createInteraction({
      appId,
    });
  }

  @Post('/end')
  @ApiSecurity('x-api-key')
  @UseGuards(ApiKeyGuard)
  endInteraction(@Body() dto: EndInteractionDto) {
    return this.interactionService.endInteraction(dto);
  }

  @Post('/event')
  @ApiSecurity('x-api-key')
  @UseGuards(ApiKeyGuard)
  createEvent(@Body() dto: CreateEventDto) {
    return this.interactionService.createEvent(dto);
  }

  @Post('/webapp/start')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('EXTERNAL_USER')
  createWebAppInteraction(@User() user: UserFromJwt) {
    return this.interactionService.createInteraction({
      userId: user.sub,
    });
  }

  @Post('/webapp/end')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('EXTERNAL_USER')
  endWebAppInteraction(@Body() dto: EndInteractionDto) {
    return this.interactionService.endInteraction(dto);
  }

  @Post('/webapp/event')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('EXTERNAL_USER')
  createWebAppEvent(@Body() dto: CreateEventDto) {
    return this.interactionService.createEvent(dto);
  }

  @Post('/webapp/connect-app-id')
  @ApiSecurity('x-api-key')
  @ApiBearerAuth()
  @UseGuards(ApiKeyGuard, JwtAuthGuard, RolesGuard)
  @Roles('EXTERNAL_USER')
  connectWithAppId(@AppId() appId: string, @Body() dto: ConnectAppIdDto) {
    return this.interactionService.connectWithAppId(appId, dto);
  }
}
