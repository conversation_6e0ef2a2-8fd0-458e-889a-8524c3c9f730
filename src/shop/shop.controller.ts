import {
  Body,
  Controller,
  Get,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { UserFromJwt } from 'src/auth/auth.type';
import { User } from 'src/auth/decorators/params.decorator';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { PurchaseCharacterDto } from './dto/purchaseCharacter.dto';
import { ShopService } from './shop.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { Roles } from 'src/auth/decorators/role.decorator';

@ApiTags('Shop')
@Controller('shop')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class ShopController {
  constructor(private readonly shopService: ShopService) {}

  @Get('items')
  @Roles('EXTERNAL_USER')
  @ApiQuery({ name: 'page', example: 1, required: true })
  @ApiQuery({ name: 'limit', example: 10, required: true })
  findItem(
    @User() user: UserFromJwt,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
  ) {
    return this.shopService.findItem(user, { page, limit });
  }

  @Post('purchase')
  @Roles('EXTERNAL_USER')
  purchaseItem(@User() user: UserFromJwt, @Body() dto: PurchaseCharacterDto) {
    return this.shopService.purchaseItem(user, dto);
  }
}
