import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CustomPrismaService } from 'nestjs-prisma';
import { PageNumberPaginationOptions } from 'prisma-extension-pagination';
import { UserFromJwt } from 'src/auth/auth.type';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { RewardService } from 'src/reward/reward.service';
import { UserCharacterService } from 'src/userCharacter/userCharacter.service';
import { PurchaseCharacterDto } from './dto/purchaseCharacter.dto';
import { TransactionType } from 'src/reward/reward.types';
import { UserCharacterType } from '@prisma/client';

@Injectable()
export class ShopService {
  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    private readonly userCharacterService: UserCharacterService,
    private readonly rewardService: RewardService,
  ) { }

  async findItem(
    user: UserFromJwt,
    paginateParams: PageNumberPaginationOptions,
  ) {
    const { availablePoints } = await this.rewardService.getPoints(user.pdvId);

    const userCharacter = await this.prisma.client.userCharacter.findMany({
      where: { userId: user.sub, type: UserCharacterType.WEBAPP },
    });

    const [paginated, pages] = await this.prisma.client.character
      .paginate({
        select: {
          id: true,
          name: true,
          displayName: true,
          price: true,
          attachments: true,
          description: true,
        },
      })
      .withPages({
        ...paginateParams,
        includePageCount: true,
      });

    return [
      paginated.map(({ id, ...rest }) => {
        if (userCharacter.some((uc) => uc.characterId === id)) {
          return {
            id,
            ...rest,
            status: 'redeemed',
          };
        }

        if (rest.price > availablePoints) {
          return {
            id,
            ...rest,
            status: 'insufficient_points',
          };
        }

        return {
          id,
          ...rest,
          status: 'available',
        };
        // some logic here
      }),
      pages,
    ];
  }

  async purchaseItem(user: UserFromJwt, dto: PurchaseCharacterDto) {
    const userCharacter = await this.prisma.client.userCharacter.findMany({
      where: {
        userId: user.sub,
        type: UserCharacterType.WEBAPP,
      },
      include: {
        character: {
          select: { name: true },
        },
      },
    });

    if (userCharacter.some((uc) => uc.character.name === dto.characterName)) {
      throw new ConflictException('Character already redeemed');
    }

    const character = await this.prisma.client.character.findUnique({
      where: { name: dto.characterName },
    });

    if (!character) {
      throw new NotFoundException('Character not found');
    }

    let availablePoints = 0;
    const getPointsResponse = await this.rewardService.getPoints(user.pdvId);
    if (getPointsResponse) {
      availablePoints = getPointsResponse.availablePoints;
    }

    if (character.price > availablePoints) {
      throw new BadRequestException('Insufficient points');
    }

    if (!character.attachments) {
      throw new InternalServerErrorException('Character attachments not found');
    }

    if (character.price > 0) {
      const redemptionResponse = await this.rewardService.redemption(user, {
        points: character.price,
      });
      availablePoints = redemptionResponse.availablePoints;
    }

    const createdUserCharacter = await this.userCharacterService.create(user, {
      characterName: dto.characterName,
      attachments: character.attachments as Record<string, string>,
      type: UserCharacterType.WEBAPP,
    });

    await this.prisma.client.transaction.create({
      data: {
        user: { connect: { id: user.sub } },
        type: TransactionType.POINTS_REDEMPTION,
        metadata: {
          text: `Redeemed ${dto.characterName.toUpperCase()}`,
          points: character.price,
        },
      },
    });

    return {
      userCharacter: createdUserCharacter,
      availablePoints,
    };
  }
}
