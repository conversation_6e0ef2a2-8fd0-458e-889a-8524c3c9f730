import { Module } from '@nestjs/common';
import { AuthModule } from 'src/auth/auth.module';
import { RewardModule } from 'src/reward/reward.module';
import { UserCharacterModule } from 'src/userCharacter/userCharacter.module';
import { ShopController } from './shop.controller';
import { ShopService } from './shop.service';

@Module({
  imports: [AuthModule, UserCharacterModule, RewardModule],
  controllers: [ShopController],
  providers: [ShopService],
})
export class ShopModule {}
