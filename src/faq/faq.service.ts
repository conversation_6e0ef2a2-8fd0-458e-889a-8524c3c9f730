import { Inject, Injectable } from '@nestjs/common';
import { CreateFaqDto } from './dto/create-faq.dto';
import { UpdateFaqDto } from './dto/update-faq.dto';
import { CustomPrismaService } from 'nestjs-prisma';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { PageNumberPaginationOptions } from 'prisma-extension-pagination';

@Injectable()
export class FaqService {
  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
  ) {}

  async create(createFaqDto: CreateFaqDto) {
    const newOrder = await this.prisma.client.fAQ.count();

    return await this.prisma.client.fAQ.create({
      data: { ...createFaqDto, order: newOrder },
    });
  }

  async findAll(paginateParams: PageNumberPaginationOptions) {
    return await this.prisma.client.fAQ
      .paginate({ orderBy: { order: 'asc' } })
      .withPages({
        ...paginateParams,
        includePageCount: true,
      });
  }

  async findOne(id: string) {
    return await this.prisma.client.fAQ.findUniqueOrThrow({
      where: { id },
    });
  }

  async update(id: string, updateFaqDto: UpdateFaqDto) {
    return await this.prisma.client.fAQ.update({
      where: { id },
      data: updateFaqDto,
    });
  }

  async updateOrder(id: string, newOrder: number) {
    const currentFaq = await this.prisma.client.fAQ.findUniqueOrThrow({
      where: { id },
    });

    if (currentFaq.order === newOrder) {
      return currentFaq;
    }

    const faqToSwap = await this.prisma.client.fAQ.findFirst({
      where: { order: newOrder },
    });

    if (!faqToSwap) {
      return currentFaq;
    }

    await this.prisma.client.fAQ.update({
      where: { id: currentFaq.id },
      data: { order: newOrder },
    });

    await this.prisma.client.fAQ.update({
      where: { id: faqToSwap.id },
      data: { order: currentFaq.order },
    });

    return true;
  }

  async remove(id: string) {
    return await this.prisma.client.fAQ.delete({
      where: { id },
    });
  }
}
