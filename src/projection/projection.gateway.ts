import { InjectQueue } from '@nestjs/bull';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { createId } from '@paralleldrive/cuid2';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import { CustomPrismaService } from 'nestjs-prisma';
import { Namespace, Server, Socket } from 'socket.io';
import { ApplicationService } from 'src/application/application.service';
import { AuthService } from 'src/auth/auth.service';
import { dateWithTz } from 'src/libs/datetime';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';

// TODO: update cors
@WebSocketGateway({
  cors: { origin: '*' },
  namespace: 'projection',
})
export class ProjectionGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  private readonly logger = new Logger(ProjectionGateway.name);

  @WebSocketServer()
  server: Server;

  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    private readonly authService: AuthService,
    private readonly applicationService: ApplicationService,
    @InjectQueue('projection-queue') private projectionQueue: Queue,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  @WebSocketServer() io: Namespace;

  afterInit(): void {
    this.logger.log(`Websocket Gateway initialized.`);
  }

  async handleConnection(client: Socket) {
    const apiKey: string = client.handshake.auth.apiKey.toString();
    const appId = await this.authService.exchangeApiKeyForAppId(apiKey);

    if (!appId) return client.disconnect();

    this.logger.debug(`Socket connected with app id: ${appId}`);
    this.logger.debug(`Number of connected sockets: ${this.io.sockets.size}`);

    this.io.to(appId).emit('projection:go-away');

    await client.join(appId);

    this.io
      .to(appId)
      .emit('projection:connected', await this.getCharacterAssets());

    this.io
      .to(appId)
      .emit('projection:background', await this.getCurrentBackground(appId));

    this.io
      .to(appId)
      .emit('projection:refresh-qr', await this.getLatestOrCreateQrCode(appId));
  }

  async handleDisconnect(client: Socket) {
    this.logger.log(`Disconnected socket id: ${client.id}`);
  }

  @SubscribeMessage('projection:characters-assets-loaded')
  async handleCharactersAssetsLoaded(@ConnectedSocket() client: Socket) {
    const apiKey: string = client.handshake.auth.apiKey.toString();
    const appId = await this.authService.exchangeApiKeyForAppId(apiKey);
    if (!appId) return client.disconnect();

    this.io
      .to(appId)
      .emit(
        'projection:load-characters',
        await this.getCurrentCharacters(appId),
      );
  }

  @SubscribeMessage('projection:character-timeout')
  async handleCharacterTimeout(@ConnectedSocket() client: Socket) {
    const apiKey: string = client.handshake.auth.apiKey.toString();
    const appId = await this.authService.exchangeApiKeyForAppId(apiKey);
    if (!appId) return client.disconnect();

    await this.projectionQueue.resume();
  }

  @SubscribeMessage('projection:character-exited')
  async handleCharacterExited(
    @MessageBody() { exitId }: { exitId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const apiKey: string = client.handshake.auth.apiKey.toString();
    const appId = await this.authService.exchangeApiKeyForAppId(apiKey);
    if (!appId) return client.disconnect();

    try {
      await this.prisma.client.projection.update({
        where: { id: exitId, appId },
        data: { exitedAt: dateWithTz().toDate(), exiting: false },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  private async generateNewProjectionQrCode() {
    for await (const appId of this.io.adapter.rooms.keys()) {
      if (!(await this.checkForValidAppId(appId))) continue;

      this.io
        .to(appId)
        .emit(
          'projection:refresh-qr',
          await this.getLatestOrCreateQrCode(appId),
        );
    }
  }

  private async getLatestOrCreateQrCode(appId: string) {
    let qrCode = await this.prisma.client.qrCode.findFirst({
      where: { type: 'projection', appId },
      orderBy: { createdAt: 'desc' },
      select: { code: true, expiredAt: true },
    });

    if (!qrCode || qrCode.expiredAt <= new Date()) {
      qrCode = await this.prisma.client.qrCode.create({
        data: {
          app: { connect: { id: appId } },
          type: 'projection',
          code: createId(),
          expiredAt: dateWithTz().add(5, 'minute').startOf('minute').toDate(),
        },
        select: { code: true, expiredAt: true },
      });
    }

    return qrCode.code;
  }

  private async getCharacterAssets() {
    return await this.prisma.client.character.findMany({
      select: { name: true },
    });
  }

  private async checkForValidAppId(appId: string) {
    try {
      return Boolean(await this.applicationService.findOne(appId));
    } catch {
      return false;
    }
  }

  private async getCurrentBackground(appId: string) {
    const setting = await this.applicationService.findOneSetting(appId);

    const { background } = setting;
    return background?.path;
  }

  private async getCurrentCharacters(appId: string) {
    const setting = await this.applicationService.findOneSetting(appId);

    const { maxCharacters, ttlCharacter } = setting;

    const currentCharacters = await this.prisma.client.projection.findMany({
      where: { appId, enteredAt: { not: null }, exitedAt: null },
      select: {
        id: true,
        userCharacter: {
          select: { character: true, type: true, createdAt: true },
        },
        customizeAttachments: true,
      },
      orderBy: { enteredAt: 'asc' },
    });

    if (currentCharacters.length > maxCharacters) {
      const shouldExit = currentCharacters.splice(
        0,
        currentCharacters.length - maxCharacters,
      );

      await this.prisma.client.projection.updateMany({
        where: { id: { in: shouldExit.map(({ id }) => id) } },
        data: { appId: null, exitedAt: dateWithTz().toDate() },
      });
    }

    const latestCharacters = await this.prisma.client.projection.findMany({
      where: { appId, enteredAt: { not: null }, exitedAt: null },
      select: {
        id: true,
        userCharacter: {
          select: { id: true, character: true, type: true, createdAt: true },
        },
        customizeAttachments: true,
      },
      orderBy: { enteredAt: 'asc' },
    });

    return latestCharacters.map(({ userCharacter, ...character }) => ({
      ...character,
      character: userCharacter.character,
      userCharacterId: userCharacter.id,
      type: userCharacter.type,
      ttl: ttlCharacter,
      createdAt: userCharacter.createdAt,
    }));
  }
}
