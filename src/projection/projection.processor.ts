import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Inject } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { CustomPrismaService } from 'nestjs-prisma';
import { ApplicationService } from 'src/application/application.service';
import { dateWithTz } from 'src/libs/datetime';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { ProjectionGateway } from './projection.gateway';

export type CharacterJobData = {
  appId: string;
  userCharacterId: string;
  projectionId: string;
};

@Processor('projection-queue')
export class ProjectionProcessor {
  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    private readonly projectionGateway: ProjectionGateway,
    private readonly applicationService: ApplicationService,
    @InjectQueue('projection-queue') private projectionQueue: Queue,
  ) {}

  @Process()
  async handleNewCharacter(job: Job<CharacterJobData>) {
    const { appId, userCharacterId, projectionId } = job.data;

    const setting = await this.applicationService.findOneSetting(appId);

    const projections = await this.prisma.client.projection.findMany({
      where: { appId, enteredAt: { not: null }, exitedAt: null },
      orderBy: { createdAt: 'desc' },
    });

    const diff = projections.length - setting.maxCharacters;

    if (diff >= 0) {
      let characterIdToExit = '';

      const projectedCharacter = projections.find(
        (projection) => projection.userCharacterId === userCharacterId,
      );

      if (projectedCharacter) {
        characterIdToExit = projectedCharacter.id;
      } else {
        const oldestCharacterOnScreen =
          await this.prisma.client.projection.findFirst({
            where: {
              appId,
              enteredAt: { not: null },
              exitedAt: null,
              exiting: false,
            },
            orderBy: { enteredAt: 'asc' },
          });

        characterIdToExit = oldestCharacterOnScreen.id;
      }

      await this.prisma.client.projection.update({
        where: { id: characterIdToExit },
        data: { exiting: true },
      });

      this.projectionGateway.server
        .to(appId)
        .emit('projection:character-exit', {
          exitId: characterIdToExit,
        });

      setTimeout(async () => {
        const projectedCharacter =
          await this.prisma.client.projection.findUnique({
            where: { id: characterIdToExit, appId, exiting: true },
          });

        if (projectedCharacter) {
          await this.prisma.client.projection.update({
            where: { id: projectedCharacter.id },
            data: { exitedAt: dateWithTz().toDate(), exiting: false },
          });
        }
      }, 10000);
    }

    const updatedCharacter = await this.prisma.client.projection.update({
      where: { id: projectionId },
      data: {
        waiting: false,
        enteredAt: dateWithTz().toDate(),
        exitedAt: null,
        exiting: false,
      },
      select: {
        id: true,
        customizeAttachments: true,
        createdAt: true,
      },
    });

    const userCharacter = await this.prisma.client.userCharacter.findUnique({
      where: { id: userCharacterId },
      select: { character: true, type: true },
    });

    this.projectionGateway.server.to(appId).emit('projection:new-character', {
      ttl: setting.ttlCharacter,
      userCharacterId,
      ...updatedCharacter,
      ...userCharacter,
    });

    if (diff + 1 >= 0) {
      this.projectionQueue.pause();
    }
  }
}
