import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Role } from '@prisma/client';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class CreateUserDto {
  @ApiProperty({
    description: 'The username for user',
    example: 'Username',
  })
  @IsString()
  username: string;

  @ApiPropertyOptional({
    description: 'The password for user',
    example: 'Password',
  })
  @IsString()
  @IsOptional()
  password?: string;

  @ApiProperty({
    description: 'The role for user',
    example: Role.USER,
  })
  @IsEnum(Role)
  role: Role;
}
