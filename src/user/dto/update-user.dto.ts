import { ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';
import { IsDateString, IsNumber, IsOptional } from 'class-validator';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiPropertyOptional({
    description: 'The last issued JWT token unix timestamp',
    example: 1620000000,
  })
  @IsOptional()
  @IsNumber()
  lastTokenIat?: number;

  @ApiPropertyOptional({
    description: 'The deactivated at unix timestamp',
    example: 1620000000,
  })
  @IsOptional()
  @IsDateString()
  deactivatedAt?: Date;
}
