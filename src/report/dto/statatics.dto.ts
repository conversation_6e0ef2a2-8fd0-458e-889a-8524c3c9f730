import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsString } from 'class-validator';

export class StatisticFilterDto {
  @ApiProperty({
    description: 'App Id',
    example: 'app-1',
  })
  @IsString()
  appId: string;

  @ApiProperty({
    description: 'Get the report data of the application from this date.',
    example: '12/24/2023',
  })
  @Type(() => Date)
  @IsDate()
  from: Date;

  @ApiProperty({
    description: 'Get the interaction of the application to this date.',
    example: '12/26/2023',
  })
  @Type(() => Date)
  @IsDate()
  to: Date;
}
