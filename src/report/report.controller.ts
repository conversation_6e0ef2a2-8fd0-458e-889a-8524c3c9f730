import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/role.decorator';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { StatisticFilterDto } from './dto/statatics.dto';
import { ReportService } from './report.service';

@ApiTags('Report')
@Controller('report')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class ReportController {
  constructor(private readonly reportservice: ReportService) {}

  @Post()
  @Roles('ADMIN')
  async getStatisticReport(@Body() dto: StatisticFilterDto) {
    return await this.reportservice.statistic(dto);
  }

  @Post('excel')
  @Roles('ADMIN')
  async getStatisticReportExcel(@Body() dto: StatisticFilterDto) {
    return await this.reportservice.generateExcel(dto);
  }

  @Post('v2')
  @Roles('ADMIN')
  async getStatisticReportV2(@Body() dto: StatisticFilterDto) {
    return await this.reportservice.statisticV2(dto);
  }

  @Post('v2/excel')
  @Roles('ADMIN')
  async getStatisticReportExcelV2(@Body() dto: StatisticFilterDto) {
    return await this.reportservice.generateExcelV2(dto);
  }
}
