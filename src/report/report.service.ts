import { Inject, Injectable } from '@nestjs/common';
import { Role, UserCharacterType } from '@prisma/client';
import * as archiver from 'archiver';
import * as archiverZipEncrypted from 'archiver-zip-encrypted';
import * as crypto from 'crypto';
import * as ExcelJS from 'exceljs';
import {
  createWriteStream,
  existsSync,
  mkdirSync,
  readFileSync,
  unlinkSync,
} from 'fs';
import { CustomPrismaService } from 'nestjs-prisma';
import { dateWithTz } from 'src/libs/datetime';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { TransactionType } from 'src/reward/reward.types';
import { EVENT_MESSAGES } from '../interaction/interaction.constant';
import { StatisticFilterDto } from './dto/statatics.dto';

export type StatisticDetail = {
  pressStartButton: number;
  submitCustomization: number;
  idleTimeout: number;
  numberOfKioskCharactersCustomizedPerHour: Record<string, number>;
  chosenCharacter: number;
  numberOfKioskCharactersGroupByName: Record<string, number>;
  numberOfKioskCharactersQueued: number;
  totalTimeSpentToCustomizeCharacterInSeconds: Record<string, number>;

  physicalCustomizedAmountPerHour: Record<string, number>;
  createdPhysicalCharacterGroupByName: Record<string, number>;
  numberOfCharacterFromPhysicalPaperQueued: number;

  numberOfWebAppCharacterQueued: number;
  numberOfWebAppCharatersDisplayedPerHour: Record<string, number>;
  numberOfWebAppCharactersDisplayedGroupByName: Record<string, number>;
  webAppScanQrCodeAmount: number;
  webAppScanQrCodeAmountUniqueUser: number;
  webAppCollectedCharacterGroupByPrice: Record<string, number>;
  socialShareAmountTaggedToLibrary: number;
  socialShareAmountUntaggedToLibrary: number;
  totalPointsPerDay: number;
};
export type Statistic = {
  kiosk: Record<string, StatisticDetail>;
  webapp: Record<string, StatisticDetail>;
  physical: Record<string, StatisticDetail>;
};

type StatisticDetailV2 = {
  kiosk: {
    [k: string]: {
      characterGroupByName: Record<string, number>;
      projectionsDetail: {
        projectionDatetime: string;
        interactionId: string;
        duration: number;
        characterName: string;
        touchpointId: string;
      }[];
    };
  };
  webapp: {
    [k: string]: {
      redeemedCharacterAmount: number;
      totalPointsUsed: number;
      redeemedCharacterGroupByName: {
        [k: string]: number;
      };
      savedDigitalCharacterGroupByName: number;
      projectedDigitalCharacterGroupByName: {
        [k: string]: number;
      };
      webappInteractionsDetail: {
        eventDatetime: string;
        pdvId: string;
        pointUsed: number;
        characterName: string;
        touchpointId: string;
        name: string;
      }[];
    };
  };
  physical: {
    [k: string]: {
      projectedCharacterGroupByName: {
        [k: string]: number;
      };
      savedPhysicalCharacterGroupByName: {
        [k: string]: number;
      };
      physicalInteractionDetail: {
        eventDatetime: string;
        pdvId: string;
        characterName: string;
        touchpointId: string;
        name: string;
      }[];
    };
  };
};

@Injectable()
export class ReportService {
  private KIOSK_QUESTION_STAT_MAP = {
    pressStartButton: 'How many unique interactions are created every day?',
    submitCustomization: 'How many new characters are created every day?',
    idleTimeout: 'How many times do users leave the kiosk while playing?',
    chosenCharacter:
      'How many characters are chosen every day, including those that have not been submitted?',
    numberOfKioskCharactersQueued:
      'How many (kiosk) characters need to wait in the queue before going to the projection wall?',
    numberOfKioskCharactersCustomizedPerHour:
      'How many (kiosk) characters are customized per hour?',
    numberOfKioskCharactersGroupByName:
      'How many (kiosk) characters have been created, grouped by name?',
    totalTimeSpentToCustomizeCharacterInSeconds:
      'How much time did the user spend at the kiosk to customize the bug (in seconds)?',
  };

  private WEBAPP_QUESTION_STAT_MAP = {
    numberOfWebAppCharacterQueued:
      'How many web app characters need to wait in the queue before being projected onto the wall?',
    numberOfWebAppCharatersDisplayedPerHour:
      'How many web app characters are displayed per hour?',
    numberOfWebAppCharactersDisplayedGroupByName:
      'How many web app characters are displayed, grouped by name?',
    webAppScanQrCodeAmountUniqueUser:
      'How many times unique users scan projection QR code every day for each library?',
    webAppScanQrCodeAmount:
      'How many times users scan projection QR code every day for each library?',
    webAppCollectedCharacterGroupByPrice:
      'How many characters are collected every day, including those that have not been submitted?',
    socialShareAmountTaggedToLibrary:
      'How many times users share their character via sharing button every day? (tagged to library)',
    socialShareAmountUntaggedToLibrary:
      'How many times users share their character via sharing button every day? (untagged to library)',
    totalPointsPerDay: 'How many points are consumed every day?',
  };

  private PHYSICAL_QUESTION_STAT_MAP = {
    numberOfCharacterFromPhysicalPaperQueued:
      'How many physical characters need to wait in the queue before going to the projection wall?',
    physicalCustomizedAmountPerHour:
      'How many physical characters are customized per hour?',
    createdPhysicalCharacterGroupByName:
      'How many physical characters have been created, grouped by name?',
  };

  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
  ) {}

  async statistic({ appId, from, to }: StatisticFilterDto) {
    const datesToReport = Array.from({
      length: dateWithTz(to).diff(from, 'days') + 1,
    }).map((_, index) => dateWithTz(from).add(index, 'day'));

    const result: Statistic = {
      kiosk: {},
      webapp: {},
      physical: {},
    };

    const reportResults = await Promise.all(
      datesToReport.map(async (date) => {
        const startOfDay = date.startOf('day').toDate();
        const endOfDay = date.endOf('day').toDate();

        // Statistic
        const createdCharacter = await this.prisma.client.projection.findMany({
          where: {
            appId,
            createdAt: {
              gte: startOfDay,
              lte: endOfDay,
            },
          },
          select: {
            userCharacter: {
              select: { character: { select: { name: true } }, type: true },
            },
            createdAt: true,
            enteredAt: true,
          },
        });

        const numberOfKioskCharactersGroupByName: Record<string, number> = {};
        const createdPhysicalCharacterGroupByName: Record<string, number> = {};
        const numberOfWebAppCharactersDisplayedGroupByName: Record<
          string,
          number
        > = {};
        let numberOfKioskCharactersQueued = 0;
        let numberOfCharacterFromPhysicalPaperQueued = 0;
        let numberOfWebAppCharacterQueued = 0;
        const numberOfKioskCharactersCustomizedPerHour: Record<string, number> =
          {
            '0:01 - 1:00': 0,
            '1:01 - 2:00': 0,
            '2:01 - 3:00': 0,
            '3:01 - 4:00': 0,
            '4:01 - 5:00': 0,
            '5:01 - 6:00': 0,
            '6:01 - 7:00': 0,
            '7:01 - 8:00': 0,
            '8:01 - 9:00': 0,
            '9:01 - 10:00': 0,
            '10:01 - 11:00': 0,
            '11:01 - 12:00': 0,
            '12:01 - 13:00': 0,
            '13:01 - 14:00': 0,
            '14:01 - 15:00': 0,
            '15:01 - 16:00': 0,
            '16:01 - 17:00': 0,
            '17:01 - 18:00': 0,
            '18:01 - 19:00': 0,
            '19:01 - 20:00': 0,
            '20:01 - 21:00': 0,
            '21:01 - 22:00': 0,
            '22:01 - 23:00': 0,
            '23:01 - 24:00': 0,
          };
        const physicalCustomizedAmountPerHour: Record<string, number> = {
          '0:01 - 1:00': 0,
          '1:01 - 2:00': 0,
          '2:01 - 3:00': 0,
          '3:01 - 4:00': 0,
          '4:01 - 5:00': 0,
          '5:01 - 6:00': 0,
          '6:01 - 7:00': 0,
          '7:01 - 8:00': 0,
          '8:01 - 9:00': 0,
          '9:01 - 10:00': 0,
          '10:01 - 11:00': 0,
          '11:01 - 12:00': 0,
          '12:01 - 13:00': 0,
          '13:01 - 14:00': 0,
          '14:01 - 15:00': 0,
          '15:01 - 16:00': 0,
          '16:01 - 17:00': 0,
          '17:01 - 18:00': 0,
          '18:01 - 19:00': 0,
          '19:01 - 20:00': 0,
          '20:01 - 21:00': 0,
          '21:01 - 22:00': 0,
          '22:01 - 23:00': 0,
          '23:01 - 24:00': 0,
        };
        const numberOfWebAppCharatersDisplayedPerHour: Record<string, number> =
          {
            '0:01 - 1:00': 0,
            '1:01 - 2:00': 0,
            '2:01 - 3:00': 0,
            '3:01 - 4:00': 0,
            '4:01 - 5:00': 0,
            '5:01 - 6:00': 0,
            '6:01 - 7:00': 0,
            '7:01 - 8:00': 0,
            '8:01 - 9:00': 0,
            '9:01 - 10:00': 0,
            '10:01 - 11:00': 0,
            '11:01 - 12:00': 0,
            '12:01 - 13:00': 0,
            '13:01 - 14:00': 0,
            '14:01 - 15:00': 0,
            '15:01 - 16:00': 0,
            '16:01 - 17:00': 0,
            '17:01 - 18:00': 0,
            '18:01 - 19:00': 0,
            '19:01 - 20:00': 0,
            '20:01 - 21:00': 0,
            '21:01 - 22:00': 0,
            '22:01 - 23:00': 0,
            '23:01 - 24:00': 0,
          };

        createdCharacter.forEach(
          ({
            userCharacter: {
              character: { name },
              type,
            },
            createdAt,
            enteredAt,
          }) => {
            // Number of characters customized/created daily (by hours)
            const fromHour = dateWithTz(createdAt).hour() + 1;
            const toHour = dateWithTz(createdAt).hour() + 2;
            const hourRange = `${fromHour}:01 - ${toHour}:00`;

            switch (type) {
              case 'KIOSK': {
                numberOfKioskCharactersCustomizedPerHour[hourRange] =
                  (numberOfKioskCharactersCustomizedPerHour[hourRange] || 0) +
                  1;

                // Count created character by name
                numberOfKioskCharactersGroupByName[name] =
                  (numberOfKioskCharactersGroupByName[name] || 0) + 1;

                // Count queued amount
                if (dateWithTz(createdAt).diff(enteredAt, 'seconds') >= 1) {
                  numberOfKioskCharactersQueued++;
                }
                break;
              }

              case 'WEBAPP': {
                numberOfWebAppCharatersDisplayedPerHour[hourRange] =
                  (numberOfWebAppCharatersDisplayedPerHour[hourRange] || 0) + 1;

                // Count created character by name
                numberOfWebAppCharactersDisplayedGroupByName[name] =
                  (numberOfWebAppCharactersDisplayedGroupByName[name] || 0) + 1;

                // Count queued amount
                if (dateWithTz(createdAt).diff(enteredAt, 'seconds') >= 1) {
                  numberOfWebAppCharacterQueued++;
                }
                break;
              }

              case 'PHYSICAL': {
                physicalCustomizedAmountPerHour[hourRange] =
                  (physicalCustomizedAmountPerHour[hourRange] || 0) + 1;

                // Count created character by name
                createdPhysicalCharacterGroupByName[name] =
                  (createdPhysicalCharacterGroupByName[name] || 0) + 1;

                // Count queued amount
                if (dateWithTz(createdAt).diff(enteredAt, 'seconds') >= 1) {
                  numberOfCharacterFromPhysicalPaperQueued++;
                }
                break;
              }
            }
          },
        );

        // Queries
        const [
          eventCounts,
          todayInteractions,
          webAppCollectedCharacter,
          uniqueUserInteractions,
          collectedCharacterTransaction,
        ] = await Promise.all([
          this.prisma.client.event.groupBy({
            by: ['name'],
            _count: {
              _all: true,
            },
            where: {
              interaction: {
                appId,
                createdAt: {
                  gte: startOfDay,
                  lte: endOfDay,
                },
              },
            },
          }),
          this.prisma.client.interaction.findMany({
            where: {
              appId,
              createdAt: {
                gte: startOfDay,
                lte: endOfDay,
              },
            },
            include: { events: true },
          }),
          this.prisma.client.userCharacter.findMany({
            where: {
              type: UserCharacterType.WEBAPP,
              createdAt: {
                gte: startOfDay,
                lte: endOfDay,
              },
            },
            select: { character: { select: { price: true } } },
          }),
          this.prisma.client.interaction.findMany({
            where: {
              appId,
              createdAt: {
                gte: startOfDay,
                lte: endOfDay,
              },
            },
            select: { id: true },
            distinct: ['userId'],
          }),
          this.prisma.client.transaction.findMany({
            where: {
              type: TransactionType.POINTS_REDEMPTION,
              createdAt: {
                gte: startOfDay,
                lte: endOfDay,
              },
            },
            select: { metadata: true },
          }),
        ]);

        const eventCountMap = {};
        eventCounts.forEach((event) => {
          eventCountMap[event.name] = event._count._all;
        });

        const pressStartButton = todayInteractions.filter(({ events }) => {
          return events.some(({ name }) =>
            [
              EVENT_MESSAGES.SELECTION_SCREEN_MSG,
              EVENT_MESSAGES.IDLE_SCREEN_MSG,
            ].includes(name),
          );
        }).length;

        const submitCustomization =
          eventCountMap[EVENT_MESSAGES.CHARACTER_SUBMITTED_MSG] || 0;
        const idleTimeout = eventCountMap[EVENT_MESSAGES.IDLE_SCREEN_MSG] || 0;
        const chosenCharacter =
          eventCountMap[EVENT_MESSAGES.CUSTOMIZATION_SCREEN_VISIT_MSG] || 0;

        const webAppScanQrCodeAmount =
          eventCountMap[EVENT_MESSAGES.USER_SCAN_LIBRARY_QR_MSG] || 0;

        const finishFullFlow = todayInteractions.filter(({ events }) => {
          return events.some(
            ({ name }) => name === EVENT_MESSAGES.CHARACTER_SUBMITTED_MSG,
          );
        });
        const totalTimeSpentToCustomizeCharacterInSeconds =
          finishFullFlow.reduce((acc, { id, createdAt, endedAt }) => {
            const seconds = dateWithTz(endedAt).diff(
              dateWithTz(createdAt),
              'seconds',
            );

            acc[id] = isNaN(seconds) ? undefined : `${seconds}s`;

            return acc;
          }, {});

        // Webapp
        const webAppCollectedCharacterGroupByPrice: Record<string, number> = {};

        webAppCollectedCharacter.forEach(({ character: { price } }) => {
          webAppCollectedCharacterGroupByPrice[`p_${price}`] =
            (webAppCollectedCharacterGroupByPrice[`p_${price}`] || 0) + 1;
        });

        const webAppScanQrCodeAmountUniqueUser =
          await this.prisma.client.event.count({
            where: {
              interactionId: { in: uniqueUserInteractions.map(({ id }) => id) },
              name: EVENT_MESSAGES.USER_SCAN_LIBRARY_QR_MSG,
              createdAt: {
                gte: startOfDay,
                lte: endOfDay,
              },
            },
          });

        const socialShareAmountTaggedToLibrary =
          eventCountMap[EVENT_MESSAGES.USER_SHARE_CHARACTER_MSG] || 0;
        const socialShareAmountUntaggedToLibrary =
          eventCountMap[EVENT_MESSAGES.USER_SHARE_CHARACTER_MSG] || 0;

        const totalPointsPerDay = collectedCharacterTransaction.reduce(
          (acc, { metadata }) => {
            const typedMetadata = metadata as { text: string; points: number };
            acc += typedMetadata.points;
            return acc;
          },
          0,
        );

        // Object.assign(result.kiosk, {
        //   [date.format('YYYY-MM-DD')]: {
        //     pressStartButton,
        //     submitCustomization,
        //     idleTimeout,
        //     chosenCharacter,
        //     numberOfKioskCharactersCustomizedPerHour,
        //     numberOfKioskCharactersGroupByName,
        //     numberOfKioskCharactersQueued,
        //     totalTimeSpentToCustomizeCharacterInSeconds,
        //   },
        // });

        // Object.assign(result.webapp, {
        //   [date.format('YYYY-MM-DD')]: {
        //     numberOfWebAppCharacterQueued,
        //     numberOfWebAppCharatersDisplayedPerHour,
        //     numberOfWebAppCharactersDisplayedGroupByName,
        //     webAppScanQrCodeAmount,
        //     webAppScanQrCodeAmountUniqueUser,
        //     webAppCollectedCharacterGroupByPrice,
        //     socialShareAmountTaggedToLibrary,
        //     socialShareAmountUntaggedToLibrary,
        //     totalPointsPerDay,
        //   },
        // });

        // Object.assign(result.physical, {
        //   [date.format('YYYY-MM-DD')]: {
        //     numberOfCharacterFromPhysicalPaperQueued,
        //     physicalCustomizedAmountPerHour,
        //     createdPhysicalCharacterGroupByName,
        //   },
        // });

        return {
          [date.format('YYYY-MM-DD')]: {
            kiosk: {
              pressStartButton,
              submitCustomization,
              idleTimeout,
              chosenCharacter,
              numberOfKioskCharactersCustomizedPerHour,
              numberOfKioskCharactersGroupByName,
              numberOfKioskCharactersQueued,
              totalTimeSpentToCustomizeCharacterInSeconds,
            },
            webapp: {
              numberOfWebAppCharacterQueued,
              numberOfWebAppCharatersDisplayedPerHour,
              numberOfWebAppCharactersDisplayedGroupByName,
              webAppScanQrCodeAmount,
              webAppScanQrCodeAmountUniqueUser,
              webAppCollectedCharacterGroupByPrice,
              socialShareAmountTaggedToLibrary,
              socialShareAmountUntaggedToLibrary,
              totalPointsPerDay,
            },
            physical: {
              numberOfCharacterFromPhysicalPaperQueued,
              physicalCustomizedAmountPerHour,
              createdPhysicalCharacterGroupByName,
            },
          },
        };
      }),
    );

    reportResults.forEach((reportResult) => {
      const [date, { kiosk, webapp, physical }] =
        Object.entries(reportResult)[0];

      Object.assign(result.kiosk, { [date]: kiosk });
      Object.assign(result.webapp, { [date]: webapp });
      Object.assign(result.physical, { [date]: physical });
    });

    return result;
  }

  async generateExcel(dto: StatisticFilterDto) {
    const statistic = await this.statistic(dto);

    const kioskBuffer = await this.generateKioskExcel(statistic.kiosk);
    const webappBuffer = await this.generateWebappExcel(statistic.webapp);
    const physicalBuffer = await this.generatePhysicalExcel(statistic.physical);

    return new Promise<{ file: Buffer; pwd: string }>((resolve) => {
      const filename = `reports.zip`;

      if (!archiver.isRegisteredFormat('zip-encrypted')) {
        archiver.registerFormat('zip-encrypted', archiverZipEncrypted);
      }

      if (!existsSync('/tmp')) mkdirSync('/tmp');

      const pwd = crypto.randomUUID().replace(/-/g, '').slice(10, 28);
      const tmpZipOutput = createWriteStream(`/tmp/${filename}`);

      const archive = archiver.create('zip-encrypted', {
        zlib: { level: 9 },
        encryptionMethod: 'aes256',
        password: pwd,
      } as archiver.ArchiverOptions & { encryptionMethod: 'aes256' });

      tmpZipOutput.on('close', async () => {
        console.log(
          'archiver has been finalized and the output file descriptor has closed.',
        );

        const resultBuffer = readFileSync(`/tmp/${filename}`);

        unlinkSync(`/tmp/${filename}`);

        resolve({
          file: resultBuffer,
          pwd,
        });
      });

      archive.on('warning', (err) => {
        if (err.code === 'ENOENT') {
          console.log('Warning: ', err);
        } else {
          console.error('Error:');
          throw err;
        }
      });

      archive.on('error', (err) => {
        console.error('Error:');
        throw err;
      });

      archive.pipe(tmpZipOutput);

      archive.append(kioskBuffer as Buffer, { name: `kiosk-report.xlsx` });
      archive.append(webappBuffer as Buffer, { name: `webapp-report.xlsx` });
      archive.append(physicalBuffer as Buffer, {
        name: `physical-report.xlsx`,
      });

      archive.finalize();
    });
  }

  async generateKioskExcel(statistic: Statistic['kiosk']) {
    const workbook = new ExcelJS.Workbook();

    Object.entries(statistic).forEach(([date, detail]) => {
      const entriesDetail = Object.entries(detail);

      const numberStats = entriesDetail.filter(
        ([_, value]) => typeof value === 'number',
      ) as [string, number][];

      const worksheet = workbook.addWorksheet(date);
      worksheet.getCell('A1').value = 'Statistic';
      worksheet
        .addTable({
          name: 'numberStats',
          ref: 'A2',
          headerRow: true,
          columns: [{ name: 'Factor' }, { name: 'Stat' }],
          rows: numberStats.map(([factor, stat]) => {
            return [this.KIOSK_QUESTION_STAT_MAP[factor], stat];
          }),
        })
        .commit();

      // numberOfKioskCharactersCustomizedPerHour
      const numberOfKioskCharactersCustomizedPerHour = entriesDetail.find(
        ([key]) => key === 'numberOfKioskCharactersCustomizedPerHour',
      )[1] as Record<string, number>;
      worksheet.getCell('D1').value =
        this.KIOSK_QUESTION_STAT_MAP.numberOfKioskCharactersCustomizedPerHour;
      worksheet
        .addTable({
          name: 'numberOfKioskCharactersCustomizedPerHour',
          ref: 'D2',
          headerRow: true,
          columns: [{ name: 'Time range' }, { name: 'Characters' }],
          rows: Object.entries(numberOfKioskCharactersCustomizedPerHour).map(
            ([timerange, characters]) => {
              return [timerange, characters];
            },
          ),
        })
        .commit();

      // numberOfKioskCharactersGroupByName
      const numberOfKioskCharactersGroupByName = entriesDetail.find(
        ([key]) => key === 'numberOfKioskCharactersGroupByName',
      )[1] as Record<string, number>;
      worksheet.getCell('G1').value =
        this.KIOSK_QUESTION_STAT_MAP.numberOfKioskCharactersGroupByName;
      worksheet
        .addTable({
          name: 'numberOfKioskCharactersGroupByName',
          ref: 'G2',
          headerRow: true,
          columns: [{ name: 'Name' }, { name: 'Amount' }],
          rows: Object.entries(numberOfKioskCharactersGroupByName).map(
            ([name, amount]) => {
              return [name, amount];
            },
          ),
        })
        .commit();

      // totalTimeSpentToCustomizeCharacterInSeconds
      const totalTimeSpentToCustomizeCharacterInSeconds = entriesDetail.find(
        ([key]) => key === 'totalTimeSpentToCustomizeCharacterInSeconds',
      )[1] as Record<string, number>;
      worksheet.getCell('J1').value =
        this.KIOSK_QUESTION_STAT_MAP.totalTimeSpentToCustomizeCharacterInSeconds;
      worksheet
        .addTable({
          name: 'totalTimeSpentToCustomizeCharacterInSeconds',
          ref: 'J2',
          headerRow: true,
          columns: [{ name: 'Interaction Id' }, { name: 'Seconds' }],
          rows: Object.entries(totalTimeSpentToCustomizeCharacterInSeconds)
            .filter(([_, seconds]) => typeof seconds === 'string')
            .map(([interactionId, seconds]) => {
              return [interactionId, seconds];
            }),
        })
        .commit();
    });

    return await workbook.xlsx.writeBuffer();
  }

  async generateWebappExcel(statistic: Statistic['webapp']) {
    const workbook = new ExcelJS.Workbook();

    Object.entries(statistic).forEach(([date, detail]) => {
      const entriesDetail = Object.entries(detail);

      const numberStats = entriesDetail.filter(
        ([_, value]) => typeof value === 'number',
      ) as [string, number][];

      const worksheet = workbook.addWorksheet(date);
      worksheet.getCell('A1').value = 'Statistic';
      worksheet
        .addTable({
          name: 'numberStats',
          ref: 'A2',
          headerRow: true,
          columns: [{ name: 'Factor' }, { name: 'Stat' }],
          rows: numberStats.map(([factor, stat]) => {
            return [this.WEBAPP_QUESTION_STAT_MAP[factor], stat];
          }),
        })
        .commit();

      // numberOfWebAppCharatersDisplayedPerHour
      const numberOfWebAppCharatersDisplayedPerHour = entriesDetail.find(
        ([key]) => key === 'numberOfWebAppCharatersDisplayedPerHour',
      )[1] as Record<string, number>;
      worksheet.getCell('D1').value =
        this.WEBAPP_QUESTION_STAT_MAP.numberOfWebAppCharatersDisplayedPerHour;
      worksheet
        .addTable({
          name: 'numberOfWebAppCharatersDisplayedPerHour',
          ref: 'D2',
          headerRow: true,
          columns: [{ name: 'Time range' }, { name: 'Characters' }],
          rows: Object.entries(numberOfWebAppCharatersDisplayedPerHour).map(
            ([timerange, characters]) => {
              return [timerange, characters];
            },
          ),
        })
        .commit();

      // numberOfWebAppCharactersDisplayedGroupByName
      const numberOfWebAppCharactersDisplayedGroupByName = entriesDetail.find(
        ([key]) => key === 'numberOfWebAppCharactersDisplayedGroupByName',
      )[1] as Record<string, number>;
      worksheet.getCell('G1').value =
        this.WEBAPP_QUESTION_STAT_MAP.numberOfWebAppCharactersDisplayedGroupByName;
      worksheet
        .addTable({
          name: 'numberOfWebAppCharactersDisplayedGroupByName',
          ref: 'G2',
          headerRow: true,
          columns: [{ name: 'Name' }, { name: 'Amount' }],
          rows: Object.entries(
            numberOfWebAppCharactersDisplayedGroupByName,
          ).map(([name, amount]) => {
            return [name, amount];
          }),
        })
        .commit();

      // webAppCollectedCharacterGroupByPrice
      const webAppCollectedCharacterGroupByPrice = entriesDetail.find(
        ([key]) => key === 'webAppCollectedCharacterGroupByPrice',
      )[1] as Record<string, number>;

      worksheet.getCell('G17').value =
        this.WEBAPP_QUESTION_STAT_MAP.webAppCollectedCharacterGroupByPrice;

      worksheet
        .addTable({
          name: 'webAppCollectedCharacterGroupByPrice',
          ref: 'G18',
          headerRow: true,
          columns: [{ name: 'Price' }, { name: 'Amount' }],
          rows: Object.entries(webAppCollectedCharacterGroupByPrice).map(
            ([price, amount]) => {
              return [price, amount];
            },
          ),
        })
        .commit();
    });

    return await workbook.xlsx.writeBuffer();
  }

  async generatePhysicalExcel(statistic: Statistic['physical']) {
    const workbook = new ExcelJS.Workbook();

    Object.entries(statistic).forEach(([date, detail]) => {
      const entriesDetail = Object.entries(detail);

      const numberStats = entriesDetail.filter(
        ([_, value]) => typeof value === 'number',
      ) as [string, number][];

      const worksheet = workbook.addWorksheet(date);
      worksheet.getCell('A1').value = 'Statistic';
      worksheet
        .addTable({
          name: 'numberStats',
          ref: 'A2',
          headerRow: true,
          columns: [{ name: 'Factor' }, { name: 'Stat' }],
          rows: numberStats.map(([factor, stat]) => {
            return [this.PHYSICAL_QUESTION_STAT_MAP[factor], stat];
          }),
        })
        .commit();

      // physicalCustomizedAmountPerHour
      const physicalCustomizedAmountPerHour = entriesDetail.find(
        ([key]) => key === 'physicalCustomizedAmountPerHour',
      )[1] as Record<string, number>;
      worksheet.getCell('D1').value =
        this.PHYSICAL_QUESTION_STAT_MAP.physicalCustomizedAmountPerHour;
      worksheet
        .addTable({
          name: 'physicalCustomizedAmountPerHour',
          ref: 'D2',
          headerRow: true,
          columns: [{ name: 'Time range' }, { name: 'Characters' }],
          rows: Object.entries(physicalCustomizedAmountPerHour).map(
            ([timerange, characters]) => {
              return [timerange, characters];
            },
          ),
        })
        .commit();

      // createdPhysicalCharacterGroupByName
      const createdPhysicalCharacterGroupByName = entriesDetail.find(
        ([key]) => key === 'createdPhysicalCharacterGroupByName',
      )[1] as Record<string, number>;
      worksheet.getCell('G1').value =
        this.PHYSICAL_QUESTION_STAT_MAP.createdPhysicalCharacterGroupByName;
      worksheet
        .addTable({
          name: 'createdPhysicalCharacterGroupByName',
          ref: 'G2',
          headerRow: true,
          columns: [{ name: 'Name' }, { name: 'Amount' }],
          rows: Object.entries(createdPhysicalCharacterGroupByName).map(
            ([name, amount]) => {
              return [name, amount];
            },
          ),
        })
        .commit();
    });

    return await workbook.xlsx.writeBuffer();
  }

  async statisticV2({ appId, from, to }: StatisticFilterDto) {
    const startOfDay = dateWithTz(from).startOf('day').toDate();
    const endOfDay = dateWithTz(to).endOf('day').toDate();

    // Queries
    const [
      projectedCharacter,
      interactions,
      charactersName,
      redemptionTransactions,
      physicalUserCharacters,
    ] = await Promise.all([
      this.prisma.client.projection.findMany({
        where: {
          appId,
          createdAt: {
            gte: startOfDay,
            lte: endOfDay,
          },
        },
        select: {
          userCharacter: {
            select: {
              id: true,
              userId: true,
              user: { select: { username: true, role: true } },
              character: { select: { name: true, price: true } },
              type: true,
            },
          },
          interactionId: true,
          createdAt: true,
          enteredAt: true,
          exitedAt: true,
        },
      }),
      this.prisma.client.interaction.findMany({
        where: {
          OR: [
            {
              appId,
              createdAt: {
                gte: startOfDay,
                lte: endOfDay,
              },
              events: { some: {} },
            },
            {
              appId: null,
              createdAt: {
                gte: startOfDay,
                lte: endOfDay,
              },
              events: { some: {} },
            },
          ],
        },
        select: {
          id: true,
          appId: true,
          userId: true,
          user: { select: { username: true, role: true } },
          events: { select: { name: true, createdAt: true } },
        },
      }),
      this.prisma.client.character.findMany({ select: { name: true } }),
      this.prisma.client.transaction.findMany({
        where: {
          type: 'POINTS_REDEMPTION',
          createdAt: {
            gte: startOfDay,
            lte: endOfDay,
          },
        },
        select: {
          metadata: true,
          user: { select: { username: true } },
          createdAt: true,
        },
      }),
      this.prisma.client.userCharacter.findMany({
        where: {
          type: 'PHYSICAL',
          source: appId,
          createdAt: { gte: startOfDay, lte: endOfDay },
        },
        select: {
          id: true,
          user: { select: { username: true } },
          character: { select: { name: true } },
          createdAt: true,
        },
      }),
    ]);

    const interactionsMap = interactions.reduce((acc, interaction) => {
      acc[interaction.id] = interaction;
      return acc;
    }, {});

    const {
      redeemedCharacterAmount,
      totalPointsUsed,
      redeemedCharacterGroupByName,
    } = redemptionTransactions.reduce(
      (acc, current) => {
        const metadata = current.metadata as {
          text: string;
          points: number;
        };

        acc.redeemedCharacterAmount += 1;
        acc.totalPointsUsed += metadata.points;
        acc.redeemedCharacterGroupByName[
          metadata.text.toLowerCase().split('redeemed ')[1]
        ] += 1;

        return acc;
      },
      {
        redeemedCharacterAmount: 0,
        totalPointsUsed: 0,
        redeemedCharacterGroupByName: Object.fromEntries(
          charactersName.map(({ name }) => [name.toLowerCase(), 0]),
        ),
      },
    );

    const userCharacterIds = interactions.reduce(
      (acc, { appId, user, events }) => {
        if (user?.role === Role.EXTERNAL_USER)
          events.forEach(async ({ name }) => {
            if (name.includes('User Saved Physical Character')) {
              const userCharacterId = name
                .split('User Saved Physical Character ')[1]
                ?.toLowerCase()
                ?.replace('{{', '')
                ?.replace('}}', '');

              acc.push(userCharacterId);
            }
          });

        return acc;
      },
      [],
    );

    const userCharacters = await this.prisma.client.userCharacter.findMany({
      where: {
        id: { in: userCharacterIds },
        source: appId,
      },
      select: {
        id: true,
        character: { select: { name: true } },
        source: true,
      },
    });
    const userCharacterMap = userCharacters.reduce((acc, uc) => {
      acc[uc.id] = uc;

      return acc;
    }, {});

    const { savedDigitalCharacterGroupByName, webappInteractionsDetail } =
      interactions.reduce(
        (acc, { appId, user, events }) => {
          if (user?.role === Role.EXTERNAL_USER)
            events.forEach(async ({ name, createdAt }) => {
              const eventDatetime = dateWithTz(createdAt).format(
                'YYYY/MM/DD HH:mm:ss',
              );

              let characterName = '-';
              let touchpointId = appId;

              // Process "User Saved Digital Character" for savedDigitalCharacterGroupByName
              if (name.includes('User Saved Digital Character')) {
                characterName = name
                  .split('User Saved Digital Character ')[1]
                  ?.toLowerCase()
                  ?.replace('{{', '')
                  ?.replace('}}', '');

                if (
                  characterName &&
                  characterName in acc.savedDigitalCharacterGroupByName
                ) {
                  acc.savedDigitalCharacterGroupByName[characterName] += 1;
                }

                name = 'Save Digital Bug to App';
              }

              // Process other event types for webappInteractionsDetail
              if (name.includes('User Purchased Character')) {
                return;
              }

              if (name.includes('User Saved Physical Character')) {
                const userCharacterId = name
                  .split('User Saved Physical Character ')[1]
                  ?.toLowerCase()
                  ?.replace('{{', '')
                  ?.replace('}}', '');

                const userCharacterInfo = userCharacterMap[userCharacterId];

                if (!userCharacterInfo) return;

                characterName = userCharacterInfo.character.name;
                touchpointId = `${userCharacterInfo.source}_scanner`;
                name = 'Save Physical Bug to App';
              }

              if (name.includes('User Shared Character')) {
                characterName = name
                  .split('User Shared Character ')[1]
                  ?.toLowerCase()
                  ?.replace('{{', '')
                  ?.replace('}}', '');
                name = 'Click Share';
              }

              if (name.includes('User Project Character')) {
                characterName = name
                  .split('User Project Character ')[1]
                  ?.toLowerCase()
                  ?.replace('{{', '')
                  ?.replace('}}', '');
                name = 'Project Digital Bug';
              }

              if (name.includes('User Shared Physical Character')) {
                characterName = name
                  .split('User Shared Physical Character ')[1]
                  ?.toLowerCase()
                  ?.replace('{{', '')
                  ?.replace('}}', '');
                name = 'Click Share';
              }

              if (name.includes('User Scanned Physical Character QR')) {
                return;
              }

              if (name.includes('User Scanned Library QR')) {
                name = 'Scan QR (Check-in)';
              }

              if (name.includes('User Logged In')) {
                name = 'Login';
              }

              acc.webappInteractionsDetail.push({
                eventDatetime,
                pdvId: user?.username,
                characterName,
                touchpointId,
                name,
              });
            });

          return acc;
        },
        {
          savedDigitalCharacterGroupByName: Object.fromEntries(
            charactersName.map(({ name }) => [name.toLowerCase(), 0]),
          ),
          webappInteractionsDetail: [],
        },
      );

    redemptionTransactions.forEach(({ user, metadata, createdAt }) => {
      if (
        (
          metadata as {
            text: string;
            points: number;
          }
        ).points
      ) {
        webappInteractionsDetail.push({
          eventDatetime: dateWithTz(createdAt).format('YYYY/MM/DD HH:mm:ss'),
          pdvId: user?.username,
          characterName: (
            metadata as {
              text: string;
              points: number;
            }
          ).text
            .toLowerCase()
            .split('redeemed ')[1],
          touchpointId: '-',
          name: 'Redeem Bug',
        });
      }
    });

    webappInteractionsDetail.sort(
      (a, b) =>
        dateWithTz(a.eventDatetime).unix() - dateWithTz(b.eventDatetime).unix(),
    );

    const physicalInteractionDetail = [];

    physicalUserCharacters.forEach(
      ({ user: { username }, character, createdAt }) => {
        physicalInteractionDetail.push({
          eventDatetime: dateWithTz(createdAt).format('YYYY/MM/DD HH:mm:ss'),
          pdvId: username,
          name: 'Project Physical Bug',
          touchpointId: appId,
          characterName: character.name,
        });

        if (username !== 'physical_anonymous') {
          physicalInteractionDetail.push({
            eventDatetime: dateWithTz(createdAt).format('YYYY/MM/DD HH:mm:ss'),
            pdvId: username,
            name: 'Save Physical Bug to App',
            touchpointId: appId,
            characterName: character.name,
          });
        }
      },
    );

    const projectedCharacterGroupByName = physicalInteractionDetail.reduce(
      (acc, { characterName, name }) => {
        if (characterName in acc && name === 'Project Physical Bug')
          acc[characterName] += 1;
        return acc;
      },
      Object.fromEntries(
        charactersName.map(({ name }) => [name.toLowerCase(), 0]),
      ),
    );

    const savedPhysicalCharacterGroupByName = physicalInteractionDetail.reduce(
      (acc, { characterName, name }) => {
        if (characterName in acc && name === 'Save Physical Bug to App')
          acc[characterName] += 1;
        return acc;
      },
      Object.fromEntries(
        charactersName.map(({ name }) => [name.toLowerCase(), 0]),
      ),
    );

    const projectionReport = projectedCharacter.reduce(
      (
        acc,
        { userCharacter, enteredAt, exitedAt, createdAt, interactionId },
      ) => {
        switch (userCharacter?.type) {
          case 'KIOSK': {
            // Datetime of the projection
            const projectionDatetime = dateWithTz(createdAt).format(
              'YYYY/MM/DD HH:mm:ss',
            );

            // interactionId
            const interaction = interactionsMap[interactionId];

            // Duration of the projection
            const duration = dateWithTz(exitedAt).diff(
              dateWithTz(enteredAt),
              'seconds',
            );

            // TouchpointID
            const touchpointId =
              interaction?.events
                ?.find(({ name }) => name.includes('|'))
                ?.name.split('|')[0] || '-';

            // Count created character by name
            acc.kiosk.characterGroupByName[
              userCharacter?.character?.name.toLowerCase()
            ] =
              (acc.kiosk.characterGroupByName[
                userCharacter?.character?.name.toLowerCase()
              ] || 0) + 1;

            // Push projection detail
            acc.kiosk.projectionsDetail.push({
              projectionDatetime,
              interactionId,
              duration,
              characterName: userCharacter.character.name,
              touchpointId,
            });

            break;
          }

          case 'WEBAPP': {
            acc.webapp.projectedDigitalCharacterGroupByName[
              userCharacter?.character?.name?.toLowerCase()
            ] =
              (acc.webapp.projectedDigitalCharacterGroupByName[
                userCharacter?.character?.name?.toLowerCase()
              ] || 0) + 1;

            break;
          }
        }

        return acc;
      },
      {
        kiosk: {
          characterGroupByName: {} as Record<string, number>,
          projectionsDetail: [] as {
            projectionDatetime: string;
            interactionId: string;
            duration: number;
            characterName: string;
            touchpointId: string;
          }[],
        },
        webapp: {
          redeemedCharacterAmount,
          totalPointsUsed,
          redeemedCharacterGroupByName,
          savedDigitalCharacterGroupByName,
          projectedDigitalCharacterGroupByName: Object.fromEntries(
            charactersName.map(({ name }) => [name.toLowerCase(), 0]),
          ),
          webappInteractionsDetail,
        },
        physical: {
          projectedCharacterGroupByName,
          savedPhysicalCharacterGroupByName,
          physicalInteractionDetail,
        },
      },
    );

    projectionReport.physical.physicalInteractionDetail.sort((a, b) => {
      return dateWithTz(a.eventDatetime).isBefore(dateWithTz(b.eventDatetime))
        ? -1
        : 1;
    });

    return projectionReport;
  }

  async generateExcelV2(dto: StatisticFilterDto) {
    const statistic = await this.statisticV2(dto);

    return new Promise<{ file: Buffer; pwd: string }>(async (resolve) => {
      const filename = `reports.zip`;

      if (!archiver.isRegisteredFormat('zip-encrypted')) {
        archiver.registerFormat('zip-encrypted', archiverZipEncrypted);
      }

      if (!existsSync('/tmp')) mkdirSync('/tmp');

      const pwd = crypto.randomUUID().replace(/-/g, '').slice(10, 28);
      const tmpZipOutput = createWriteStream(`/tmp/${filename}`);

      const archive = archiver.create('zip-encrypted', {
        zlib: { level: 9 },
        encryptionMethod: 'aes256',
        password: pwd,
      } as archiver.ArchiverOptions & { encryptionMethod: 'aes256' });

      tmpZipOutput.on('close', async () => {
        console.log(
          'archiver has been finalized and the output file descriptor has closed.',
        );

        const resultBuffer = readFileSync(`/tmp/${filename}`);

        unlinkSync(`/tmp/${filename}`);

        resolve({
          file: resultBuffer,
          pwd,
        });
      });

      archive.on('warning', (err) => {
        if (err.code === 'ENOENT') {
          console.log('Warning: ', err);
        } else {
          console.error('Error:');
          throw err;
        }
      });

      archive.on('error', (err) => {
        console.error('Error:');
        throw err;
      });

      archive.pipe(tmpZipOutput);

      const { kiosk, webapp, physical } = statistic;

      const workbook = new ExcelJS.Workbook();

      // Excel for kiosk
      const kioskWs = workbook.addWorksheet('Kiosk');
      kioskWs
        .addTable({
          name: 'projectionsDetail',
          ref: 'A1',
          headerRow: true,
          columns: [
            { name: 'Datetime' },
            { name: 'Interaction Id' },
            { name: 'Duration' },
            { name: 'Bug Name' },
            { name: 'Touchpoint ID' },
          ],
          rows: kiosk.projectionsDetail.map(
            ({
              projectionDatetime,
              interactionId,
              duration,
              characterName,
              touchpointId,
            }) => {
              return [
                projectionDatetime,
                interactionId,
                duration,
                characterName,
                touchpointId,
              ];
            },
          ),
        })
        .commit();

      kioskWs
        .addTable({
          name: 'characterGroupByName',
          ref: 'G1',
          headerRow: true,
          columns: [{ name: 'Name' }, { name: 'Number of bugs' }],
          rows: Object.entries(kiosk.characterGroupByName),
        })
        .commit();

      // Excel for webapp
      const webappWs = workbook.addWorksheet('Mobile App');
      webappWs
        .addTable({
          name: 'projectionsDetail',
          ref: 'A1',
          headerRow: true,
          columns: [
            { name: 'Datetime' },
            { name: 'PDVID' },
            { name: 'Bug Name' },
            { name: 'Touchpoint ID' },
            { name: 'Activity' },
          ],
          rows: webapp.webappInteractionsDetail.map(
            ({ eventDatetime, pdvId, characterName, touchpointId, name }) => {
              return [eventDatetime, pdvId, characterName, touchpointId, name];
            },
          ),
        })
        .commit();

      webappWs.getCell('H1').value = 'No of bugs redeemed';
      webappWs.getCell('H2').value = webapp.redeemedCharacterAmount;

      webappWs.getCell('I1').value = 'Points used';
      webappWs.getCell('I2').value = webapp.totalPointsUsed;

      webappWs
        .addTable({
          name: 'redeemedCharacterGroupByName',
          ref: 'K1',
          headerRow: true,
          columns: [{ name: 'Name' }, { name: 'Number of bugs (Redemption)' }],
          rows: Object.entries(webapp.redeemedCharacterGroupByName),
        })
        .commit();

      webappWs
        .addTable({
          name: 'savedDigitalCharacterGroupByName',
          ref: 'N1',
          headerRow: true,
          columns: [
            { name: 'Name' },
            { name: 'Number of bugs (Saving of Digital bug)' },
          ],
          rows: Object.entries(webapp.savedDigitalCharacterGroupByName),
        })
        .commit();

      webappWs
        .addTable({
          name: 'projectedDigitalCharacterGroupByName',
          ref: 'Q1',
          headerRow: true,
          columns: [{ name: 'Name' }, { name: 'Number of bugs (Projected)' }],
          rows: Object.entries(webapp.projectedDigitalCharacterGroupByName),
        })
        .commit();

      // Excel for physical
      const physicalWs = workbook.addWorksheet('Physical');
      physicalWs
        .addTable({
          name: 'projectionsDetail',
          ref: 'A1',
          headerRow: true,
          columns: [
            { name: 'Datetime' },
            { name: 'PDVID' },
            { name: 'Activity ' },
            { name: 'Touchpoint ID' },
            { name: 'Bug Name' },
          ],
          rows: physical.physicalInteractionDetail.map(
            ({ eventDatetime, pdvId, name, touchpointId, characterName }) => {
              return [eventDatetime, pdvId, name, touchpointId, characterName];
            },
          ),
        })
        .commit();

      physicalWs
        .addTable({
          name: 'projectedCharacterGroupByName',
          ref: 'G1',
          headerRow: true,
          columns: [
            { name: 'Name' },
            { name: 'Number of bugs (Breakdown of Projected Physical bugs)' },
          ],
          rows: Object.entries(physical.projectedCharacterGroupByName),
        })
        .commit();

      physicalWs
        .addTable({
          name: 'savedPhysicalCharacterGroupByName',
          ref: 'J1',
          headerRow: true,
          columns: [
            { name: 'Name' },
            { name: 'Number of bugs (Breakdown of Saving Physical bugs)' },
          ],
          rows: Object.entries(physical.savedPhysicalCharacterGroupByName),
        })
        .commit();

      const buffer = await workbook.xlsx.writeBuffer();

      archive.append(buffer as Buffer, { name: `reportv2.xlsx` });

      archive.finalize();
    });
  }
}
