import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common';
import * as sanitizeHtml from 'sanitize-html';

@Injectable()
export class SanitizeHtmlPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata) {
    if (typeof value === 'object' && value !== null) {
      // Sanitize each string field in the object
      for (const key in value) {
        if (typeof value[key] === 'string') {
          value[key] = sanitizeHtml(value[key]);
        }
      }
    } else if (typeof value === 'string') {
      value = sanitizeHtml(value);
    }
    return value;
  }
}
