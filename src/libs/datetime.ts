import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import * as isBetween from 'dayjs/plugin/isBetween';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);

dayjs.tz.setDefault('Asia/Singapore');

export const dateWithTz = dayjs.tz;

export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};
