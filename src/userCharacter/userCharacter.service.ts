import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { createWriteStream, existsSync, mkdirSync } from 'fs';
import { CustomPrismaService } from 'nestjs-prisma';
import { join } from 'path';
import { PageNumberPaginationOptions } from 'prisma-extension-pagination';
import { UserFromJwt } from 'src/auth/auth.type';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { CreateUserCharacterDto } from './dto/create-user-character.dto';
import { UpdateUserCharacterDto } from './dto/update-user-character.dto';
import { UserCharacterType } from '@prisma/client';
import { TransferOwnerUserCharacterDto } from 'src/userCharacter/dto/transfer-owner-user-character.dto';

@Injectable()
export class UserCharacterService {
  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
  ) {}

  async create(user: UserFromJwt, dto: CreateUserCharacterDto) {
    const newUserCharacter = await this.prisma.client.userCharacter.create({
      data: {
        user: { connect: { id: user.sub } },
        type: dto.type,
        attachments: dto.attachments,
        character: {
          connect: {
            name: dto.characterName.toLowerCase(),
          },
        },
        source: 'webapp',
      },
      include: {
        character: {
          select: {
            name: true,
          },
        },
      },
    });

    return newUserCharacter;
  }

  async findAll(
    user: UserFromJwt,
    paginateParams: PageNumberPaginationOptions,
    type: UserCharacterType,
  ) {
    return this.prisma.client.userCharacter
      .paginate({
        where: { userId: user.sub, type },
        include: {
          character: {
            select: { name: true },
          },
        },
      })
      .withPages({
        ...paginateParams,
        includePageCount: true,
      });
  }

  async findOne(user: UserFromJwt, id: string) {
    const character = await this.prisma.client.userCharacter.findUniqueOrThrow({
      where: { id, userId: user.sub },
      include: {
        character: {
          select: {
            name: true,
            characterColor: {
              include: {
                color: true,
              },
            },
          },
        },
      },
    });

    return character;
  }

  async uploadAvatar(file: Express.Multer.File) {
    const storagePath = join(process.cwd(), 'public', 'avatars');

    if (!existsSync(storagePath)) {
      mkdirSync(storagePath);
    }

    const writeStream = createWriteStream(join(storagePath, file.originalname));
    writeStream.write(file.buffer);

    return {
      status: 'success',
      message: 'File uploaded successfully',
    };
  }

  async update(user: UserFromJwt, id: string, dto: UpdateUserCharacterDto) {
    const updatedCharacter = await this.prisma.client.userCharacter.update({
      where: { id, userId: user.sub },
      data: dto,
    });
    return updatedCharacter;
  }

  async transferOwner(
    user: UserFromJwt,
    { userCharacterId }: TransferOwnerUserCharacterDto,
  ) {
    // First check who owns the character
    const userCharacter =
      await this.prisma.client.userCharacter.findUniqueOrThrow({
        where: { id: userCharacterId },
        include: { user: true },
      });

    if (userCharacter.user.role !== 'ANONYMOUS') {
      throw new BadRequestException(
        'You cannot transfer ownership of this character',
      );
    }

    // Then transfer ownership
    await this.prisma.client.userCharacter.update({
      where: { id: userCharacterId },
      data: { user: { connect: { id: user.sub } } },
    });

    return {
      status: 'success',
      message: 'Successfully transferred ownership of character',
    };
  }
}
