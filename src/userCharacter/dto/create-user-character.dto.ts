import { ApiProperty } from '@nestjs/swagger';
import { UserCharacterType } from '@prisma/client';
import { IsEnum, IsObject, IsString } from 'class-validator';

export class CreateUserCharacterDto {
  @ApiProperty({
    description: 'The name of the character',
    example: 'wintrix',
  })
  @IsString()
  characterName: string;

  @ApiProperty({
    description: 'The character customize attachments object',
    example: {
      r_leg: 'r_leg_white',
      r_arm2: 'r_arm2_white',
      r_arm1: 'r_arm1_white',
      body: 'body_white',
      l_leg: 'l_leg_white',
      l_arm2: 'l_arm2_white',
      l_arm1: 'l_arm1_white',
      head: 'head_white',
    },
  })
  @IsObject()
  attachments: Record<string, string>;

  @ApiProperty({
    description: 'The type of the character',
    example: 'WEBAPP',
  })
  @IsEnum(UserCharacterType)
  type: UserCharacterType;
}
