import { Module } from '@nestjs/common';
import { UserCharacterService } from './userCharacter.service';
import { UserCharacterController } from './userCharacter.controller';
import { AuthModule } from 'src/auth/auth.module';

@Module({
  imports: [AuthModule],
  controllers: [UserCharacterController],
  providers: [UserCharacterService],
  exports: [UserCharacterService],
})
export class UserCharacterModule {}
