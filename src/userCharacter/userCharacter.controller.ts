import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiConsumes, ApiQuery, ApiTags } from '@nestjs/swagger';
import { UserCharacterType } from '@prisma/client';
import { memoryStorage } from 'multer';
import { UserFromJwt } from 'src/auth/auth.type';
import { User } from 'src/auth/decorators/params.decorator';
import { Roles } from 'src/auth/decorators/role.decorator';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { CreateUserCharacterDto } from './dto/create-user-character.dto';
import { UpdateUserCharacterDto } from './dto/update-user-character.dto';
import { UserCharacterService } from './userCharacter.service';
import { TransferOwnerUserCharacterDto } from 'src/userCharacter/dto/transfer-owner-user-character.dto';

@ApiTags('UserCharacter')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('user-character')
export class UserCharacterController {
  constructor(private readonly userCharacterService: UserCharacterService) {}

  @Post()
  @Roles('EXTERNAL_USER')
  create(@User() user: UserFromJwt, @Body() dto: CreateUserCharacterDto) {
    return this.userCharacterService.create(user, dto);
  }

  @Get()
  @Roles('EXTERNAL_USER')
  @ApiQuery({ name: 'page', example: 1, required: true })
  @ApiQuery({ name: 'limit', example: 10, required: true })
  findAll(
    @User() user: UserFromJwt,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
    @Query('type') type: UserCharacterType = 'WEBAPP',
  ) {
    return this.userCharacterService.findAll(user, { page, limit }, type);
  }

  @Get(':id')
  @Roles('EXTERNAL_USER')
  findOne(@User() user: UserFromJwt, @Param('id') id: string) {
    return this.userCharacterService.findOne(user, id);
  }

  @ApiConsumes('multipart/form-data')
  @Post('avatar')
  @Roles('EXTERNAL_USER')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
    }),
  )
  async uploadAvatar(@UploadedFile() file: Express.Multer.File) {
    return await this.userCharacterService.uploadAvatar(file);
  }

  @Patch(':id')
  @Roles('EXTERNAL_USER')
  update(
    @User() user: UserFromJwt,
    @Param('id') id: string,
    @Body() dto: UpdateUserCharacterDto,
  ) {
    return this.userCharacterService.update(user, id, dto);
  }

  @Post('/transfer')
  @Roles('EXTERNAL_USER')
  async transferOwner(
    @User() user: UserFromJwt,
    @Body() dto: TransferOwnerUserCharacterDto,
  ) {
    return this.userCharacterService.transferOwner(user, dto);
  }
}
