import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ApplicationModule } from 'src/application/application.module';
import { AuthModule } from 'src/auth/auth.module';
import { ProjectionModule } from 'src/projection/projection.module';
import { ColoringController } from './coloring.controller';
import { ColoringService } from './coloring.service';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'projection-queue',
    }),
    AuthModule,
    ApplicationModule,
    ProjectionModule,
  ],
  controllers: [ColoringController],
  providers: [ColoringService],
})
export class ColoringModule {}
