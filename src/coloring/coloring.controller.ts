import {
  Body,
  Controller,
  Get,
  HttpStatus,
  ParseFilePipeBuilder,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { UserFromJwt } from 'src/auth/auth.type';
import { User } from 'src/auth/decorators/params.decorator';
import { Roles } from 'src/auth/decorators/role.decorator';
import { ApiKeyGuard } from 'src/auth/guards/apikey.guard';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { AppId } from 'src/libs/decorators/app_id.decorator';
import { ColoringService } from './coloring.service';
import { CreateCustomizeCharacterAnonDto } from './dto/create-customize-character-anon.dto';
import { DisplayNlbCustomCharacterDto } from './dto/display-nlb-custom-character.dto';

@ApiTags('Coloring')
@Controller('coloring')
export class ColoringController {
  constructor(private readonly coloringService: ColoringService) {}

  @Post('customize-character')
  @ApiSecurity('x-api-key')
  @UseGuards(ApiKeyGuard)
  async createNewCustomizeCharacterAnon(
    @AppId() appId: string,
    @Body() dto: CreateCustomizeCharacterAnonDto,
  ) {
    return await this.coloringService.createNewCustomizeCharacterAnon(
      appId,
      dto,
    );
  }

  @ApiSecurity('x-api-key')
  @UseGuards(ApiKeyGuard, JwtAuthGuard, RolesGuard)
  @Roles('EXTERNAL_USER')
  @Post('/display-nlb-character')
  async displayNlbCustomCharacter(
    @AppId() appId: string,
    @User() user: UserFromJwt,
    @Body() dto: DisplayNlbCustomCharacterDto,
  ) {
    return await this.coloringService.displayNlbCustomCharacter(
      appId,
      user,
      dto,
    );
  }

  @ApiSecurity('x-api-key')
  @ApiBearerAuth()
  @UseGuards(ApiKeyGuard, JwtAuthGuard, RolesGuard)
  @Roles('EXTERNAL_USER')
  @Get('/last-display-nlb-character')
  async getLastDisplayNlbCustomCharacter(
    @AppId() appId: string,
    @User() user: UserFromJwt,
  ) {
    return await this.coloringService.getLastDisplayNlbCustomCharacter(
      appId,
      user,
    );
  }

  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('EXTERNAL_USER')
  @Get('/physical-event')
  async getPhysicalEventInfo() {
    return await this.coloringService.physicalEventInfo();
  }

  @Post('/physical/upload')
  @ApiSecurity('x-api-key')
  @UseGuards(ApiKeyGuard)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async receiveFileFromScanner(
    @AppId() appId: string,
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({ fileType: 'png' })
        .addMaxSizeValidator({ maxSize: 20000000 })
        .build({ errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY }),
    )
    file: Express.Multer.File,
  ) {
    return this.coloringService.createNewPhysicalCustomizeCharacterAnon(
      appId,
      file,
    );
  }
}
