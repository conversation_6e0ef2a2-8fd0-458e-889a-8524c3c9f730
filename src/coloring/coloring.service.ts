import { InjectQueue } from '@nestjs/bull';
import {
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { UserCharacterType } from '@prisma/client';
import { Queue } from 'bull';
import { Image, createCanvas, loadImage } from 'canvas';
import { copyFileSync, existsSync, mkdirSync, readFileSync } from 'fs';
import jsQR from 'jsqr';
import { CustomPrismaService } from 'nestjs-prisma';
import { join } from 'path';
import * as sharp from 'sharp';
import { ApplicationService } from 'src/application/application.service';
import { dateWithTz } from 'src/libs/datetime';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { ProjectionGateway } from 'src/projection/projection.gateway';
import { CharacterJobData } from 'src/projection/projection.processor';
import {
  Attachment,
  CharacterJSON,
  CharacterPaperSize,
  Skeleton,
} from './coloring.type';
import { CreateCustomizeCharacterAnonDto } from './dto/create-customize-character-anon.dto';
import { UserFromJwt } from 'src/auth/auth.type';
import { DisplayNlbCustomCharacterDto } from './dto/display-nlb-custom-character.dto';

@Injectable()
export class ColoringService {
  private readonly logger = new Logger(ColoringService.name);

  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    private readonly projectionGateway: ProjectionGateway,
    private readonly applicationService: ApplicationService,
    @InjectQueue('projection-queue')
    private projectionQueue: Queue<CharacterJobData>,
  ) {}

  async createNewCustomizeCharacterAnon(
    appId: string,
    {
      characterName,
      customizeAttachments,
      interactionId,
    }: CreateCustomizeCharacterAnonDto,
  ) {
    const setting = await this.applicationService.findOneSetting(appId);

    const enteredCharacters = await this.prisma.client.projection.findMany({
      where: {
        appId,
        enteredAt: { not: null },
        exitedAt: null,
        exiting: false,
      },
    });

    const isProjectionAvailable =
      enteredCharacters.length < setting.maxCharacters;

    const { id: newUserCharacterId, ...newUserCharacter } =
      await this.prisma.client.userCharacter.create({
        data: {
          user: {
            connectOrCreate: {
              where: { username: 'anonymous' },
              create: { username: 'anonymous' },
            },
          },
          type: UserCharacterType.KIOSK,
          character: { connect: { name: characterName.toLocaleLowerCase() } },
          attachments: customizeAttachments,
          source: appId,
        },
        select: {
          id: true,
          character: true,
          type: true,
        },
      });

    const newProjection = await this.prisma.client.projection.create({
      data: {
        app: { connect: { id: appId } },
        interaction: interactionId ? { connect: { id: interactionId } } : undefined,
        userCharacter: { connect: { id: newUserCharacterId } },
        customizeAttachments,
        enteredAt: isProjectionAvailable ? dateWithTz().toDate() : null,
      },
      select: {
        id: true,
        customizeAttachments: true,
        createdAt: true,
      },
    });

    if (isProjectionAvailable) {
      setTimeout(() => {
        this.projectionGateway.server
          .to(appId)
          .emit('projection:new-character', {
            ttl: setting.ttlCharacter,
            ...newProjection,
            ...newUserCharacter,
          });
      }, 3000);
    } else {
      const hasTimedOutCharacters = enteredCharacters.some(({ enteredAt }) => {
        return (
          dateWithTz().diff(dateWithTz(enteredAt), 'seconds') >=
          setting.ttlCharacter
        );
      });

      if (hasTimedOutCharacters) {
        await this.projectionQueue.resume();
      } else {
        await this.projectionQueue.pause();
      }

      await this.prisma.client.projection.update({
        where: { id: newProjection.id },
        data: { waiting: true },
      });

      await this.projectionQueue.add(
        {
          appId,
          userCharacterId: newUserCharacterId,
          projectionId: newProjection.id,
        },
        { removeOnComplete: true, delay: hasTimedOutCharacters ? 3000 : 0 },
      );
    }

    return 'ok';
  }

  async displayNlbCustomCharacter(
    appId: string,
    user: UserFromJwt,
    displayNlbCustomCharacterDto: DisplayNlbCustomCharacterDto,
  ) {
    const setting = await this.applicationService.findOneSetting(appId);

    const userCharacters = await this.prisma.client.userCharacter.findMany({
      where: { userId: user.sub },
      select: { id: true, character: { select: { id: true } } },
    });

    const projectedCharacters = await this.prisma.client.projection.findMany({
      where: {
        userCharacterId: { in: userCharacters.map(({ id }) => id) },
        enteredAt: { not: null },
        exitedAt: null,
        exiting: false,
      },
      select: {
        id: true,
        waiting: true,
        enteredAt: true,
        userCharacterId: true,
      },
      orderBy: {
        enteredAt: 'desc',
      },
      take: setting.maxCharacters,
    });

    const mappedUserCharacters = projectedCharacters.map(
      (projectedCharacter) => ({
        id: projectedCharacter.id,
        waitingInQueue: projectedCharacter.waiting,
        secondsSinceLastDisplay: dateWithTz().diff(
          dateWithTz(projectedCharacter.enteredAt),
          'seconds',
        ),
        userCharacterId: projectedCharacter.userCharacterId,
      }),
    );

    const displayedCharacter = mappedUserCharacters.find(
      ({ userCharacterId }) =>
        userCharacterId === displayNlbCustomCharacterDto.userCharacterId,
    );

    if (
      displayedCharacter &&
      (displayedCharacter.secondsSinceLastDisplay <
        setting.projectionLockTimeout ||
        displayedCharacter.waitingInQueue)
    ) {
      return 'ok';
    }

    const enteredCharacters = await this.prisma.client.projection.findMany({
      where: {
        appId,
        enteredAt: { not: null },
        exitedAt: null,
        exiting: false,
      },
    });

    const isProjectionAvailable =
      enteredCharacters.length < setting.maxCharacters;

    const userCharacter = await this.prisma.client.userCharacter.findUnique({
      where: {
        id: displayNlbCustomCharacterDto.userCharacterId,
        userId: user.sub,
      },
      select: {
        id: true,
        character: true,
        attachments: true,
        type: true,
      },
    });

    if (!userCharacter) {
      throw new NotFoundException('Character not found!');
    }

    const {
      id: userCharacterId,
      attachments,
      ...currentUserCharacter
    } = userCharacter;

    const newProjection = await this.prisma.client.projection.create({
      data: {
        app: { connect: { id: appId } },
        interaction: {
          connect: { id: displayNlbCustomCharacterDto.interactionId },
        },
        userCharacter: { connect: { id: userCharacterId } },
        customizeAttachments: attachments,
        enteredAt: isProjectionAvailable ? dateWithTz().toDate() : null,
      },
      select: {
        id: true,
        customizeAttachments: true,
        createdAt: true,
      },
    });

    if (isProjectionAvailable) {
      if (displayedCharacter) {
        await this.prisma.client.projection.update({
          where: { id: displayedCharacter.id },
          data: { exiting: true },
        });

        this.projectionGateway.server
          .to(appId)
          .emit('projection:character-exit', {
            exitId: displayedCharacter.id,
          });

        setTimeout(async () => {
          const projectedCharacter =
            await this.prisma.client.projection.findUnique({
              where: { id: displayedCharacter.id, appId, exiting: true },
            });

          if (projectedCharacter) {
            await this.prisma.client.projection.update({
              where: { id: projectedCharacter.id },
              data: { exitedAt: dateWithTz().toDate(), exiting: false },
            });
          }
        }, 10000);
      }

      setTimeout(async () => {
        this.projectionGateway.server
          .to(appId)
          .emit('projection:new-character', {
            ttl: setting.ttlCharacter,
            userCharacterId,
            ...newProjection,
            ...currentUserCharacter,
          });
      }, 5000);
    } else {
      const hasTimedOutCharacters = enteredCharacters.some(({ enteredAt }) => {
        return (
          dateWithTz().diff(dateWithTz(enteredAt), 'seconds') >=
          setting.ttlCharacter
        );
      });

      if (hasTimedOutCharacters) {
        await this.projectionQueue.resume();
      } else {
        await this.projectionQueue.pause();
      }

      await this.prisma.client.projection.update({
        where: { id: newProjection.id },
        data: { waiting: true },
      });
      await this.projectionQueue.add(
        { appId, userCharacterId, projectionId: newProjection.id },
        { removeOnComplete: true, delay: hasTimedOutCharacters ? 3000 : 0 },
      );
    }

    return 'ok';
  }

  async getLastDisplayNlbCustomCharacter(appId: string, user: UserFromJwt) {
    const setting = await this.applicationService.findOneSetting(appId);

    const userCharacters = await this.prisma.client.userCharacter.findMany({
      where: { userId: user.sub },
      select: {
        id: true,
        character: { select: { name: true } },
      },
    });

    const projectedCharacters = await this.prisma.client.projection.findMany({
      where: {
        userCharacterId: { in: userCharacters.map(({ id }) => id) },
        enteredAt: { not: null },
        exitedAt: null,
        exiting: false,
      },
      select: {
        id: true,
        userCharacter: { select: { character: { select: { name: true } } } },
        userCharacterId: true,
        waiting: true,
        enteredAt: true,
      },
      orderBy: {
        enteredAt: 'desc',
      },
      take: setting.maxProjectionCharacterPerAccount,
    });

    return {
      data: userCharacters
        ? {
            timeout: setting.projectionLockTimeout,
            blocked:
              userCharacters.length >=
                setting.maxProjectionCharacterPerAccount &&
              projectedCharacters.every(
                ({ enteredAt, waiting }) =>
                  dateWithTz().diff(dateWithTz(enteredAt), 'seconds') <
                    setting.projectionLockTimeout || waiting,
              ),
            characters: projectedCharacters.map((projectedCharacter) => ({
              userCharacterId: projectedCharacter.userCharacterId,
              name: projectedCharacter.userCharacter.character.name,
              waitingInQueue: projectedCharacter.waiting,
              secondsSinceLastDisplay: dateWithTz().diff(
                dateWithTz(projectedCharacter.enteredAt),
                'seconds',
              ),
            })),
          }
        : null,
    };
  }

  async createNewPhysicalCustomizeCharacterAnon(
    appId: string,
    file: Express.Multer.File,
  ) {
    try {
      const { characterName, fileBuffer, rawFileBuffer, templateRotation } =
        await this.processRawImage(file.buffer);

      const setting = await this.applicationService.findOneSetting(appId);

      const enteredCharacters = await this.prisma.client.projection.findMany({
        where: {
          appId,
          enteredAt: { not: null },
          exitedAt: null,
          exiting: false,
        },
      });

      const isProjectionAvailable =
        enteredCharacters.length < setting.maxCharacters;

      const { id: newUserCharacterId, ...newUserCharacter } =
        await this.prisma.client.userCharacter.create({
          data: {
            user: {
              connectOrCreate: {
                where: { username: 'physical_anonymous' },
                create: { username: 'physical_anonymous' },
              },
            },
            type: UserCharacterType.PHYSICAL,
            character: { connect: { name: characterName } },
            source: appId,
          },
          select: {
            id: true,
            character: true,
            type: true,
          },
        });

      const newProjection = await this.prisma.client.projection.create({
        data: {
          app: { connect: { id: appId } },
          userCharacter: { connect: { id: newUserCharacterId } },
          enteredAt: isProjectionAvailable ? dateWithTz().toDate() : null,
        },
        select: {
          id: true,
          createdAt: true,
        },
      });

      await this.generateAssetFromRawFile({
        ...(await this.storeRawFile({
          id: newUserCharacterId,
          characterName: newUserCharacter.character.name,
          rawFileBuffer,
          fileBuffer,
        })),
        templateRotation,
      });

      if (isProjectionAvailable) {
        setTimeout(() => {
          this.projectionGateway.server
            .to(appId)
            .emit('projection:new-character', {
              ttl: setting.ttlCharacter,
              ...newProjection,
              userCharacterId: newUserCharacterId,
              ...newUserCharacter,
            });
        }, 3000);
      } else {
        const hasTimedOutCharacters = enteredCharacters.some(
          ({ enteredAt }) => {
            return (
              dateWithTz().diff(dateWithTz(enteredAt), 'seconds') >=
              setting.ttlCharacter
            );
          },
        );

        if (hasTimedOutCharacters) {
          await this.projectionQueue.resume();
        } else {
          await this.projectionQueue.pause();
        }

        await this.prisma.client.projection.update({
          where: { id: newProjection.id },
          data: { waiting: true },
        });

        await this.projectionQueue.add(
          {
            appId,
            projectionId: newProjection.id,
            userCharacterId: newUserCharacterId,
          },
          { removeOnComplete: true, delay: hasTimedOutCharacters ? 3000 : 0 },
        );
      }

      return {
        userCharacterId: newUserCharacterId,
      };
    } catch (error) {
      throw new InternalServerErrorException({
        error: `Unable to generate asset! Please try again!`,
        message: error.message,
      });
    }
  }

  async physicalEventInfo() {
    const activeDates = [
      // real below
      dateWithTz('2024-11-22'),
      dateWithTz('2024-11-29'),
      dateWithTz('2024-12-06'),
      dateWithTz('2024-12-13'),
      dateWithTz('2024-12-20'),
      dateWithTz('2024-12-27'),
    ];

    const now = dateWithTz();

    const isActive = activeDates.some((date) => {
      return date.isSame(now, 'day');
    });

    const isOutOfLastDate =
      now.diff(activeDates[activeDates.length - 1], 'day') > 0;

    return {
      now: now.toDate(),
      isActive,
      isOutOfLastDate,
    };
  }

  private async scanQRCode(fileBuffer: Buffer) {
    try {
      const image = sharp(fileBuffer);
      const { width } = await image.metadata();

      const { data, info } = await sharp(fileBuffer)
        .ensureAlpha()
        .raw()
        .extract({
          left: 0,
          top: 0,
          width: Math.round(width * 0.13),
          height: Math.round(width * 0.13),
        })
        .toBuffer({ resolveWithObject: true });

      // Get the image data
      const imageData = {
        data: new Uint8ClampedArray(data.buffer),
        width: info.width,
        height: info.height,
      };

      // Use jsQR to decode the QR code
      const decodedQR = jsQR(
        imageData.data,
        imageData.width,
        imageData.height,
        {
          inversionAttempts: 'dontInvert',
        },
      );

      if (!decodedQR) {
        throw new NotFoundException('QR code not found in the image.');
      }

      return decodedQR;
    } catch (error) {
      this.logger.error('Error when scanning QR code:', error);
      throw new InternalServerErrorException('QR code not found in the image.');
    }
  }

  private async borderDetectionCrop(fileBuffer: Buffer) {
    const image = sharp(fileBuffer);

    // Get image metadata to understand its dimensions
    const { width, height } = await image.metadata();

    // Convert to grayscale and threshold
    const thresholdedBuffer = await image
      .grayscale()
      .threshold(100) // Converts to binary image: 0 (black) and 255 (white)
      .toBuffer();

    // Load the thresholded image data to find borders
    const { data } = await sharp(thresholdedBuffer)
      .raw()
      .toBuffer({ resolveWithObject: true });

    let left = width,
      right = 0,
      top = height,
      bottom = 0;

    const blackThreshold = 80;

    // Detect top border
    for (let y = 0; y < height; y++) {
      let blackPixelCount = 0;
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * 3;
        const [r, g, b] = [data[index], data[index + 1], data[index + 2]];
        if (r < blackThreshold && g < blackThreshold && b < blackThreshold)
          blackPixelCount++;
      }
      if (blackPixelCount >= 1500) {
        top = y;
        break;
      }
    }

    // Detect bottom border
    for (let y = height - 1; y >= 0; y--) {
      let blackPixelCount = 0;
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * 3;
        const [r, g, b] = [data[index], data[index + 1], data[index + 2]];
        if (r < blackThreshold && g < blackThreshold && b < blackThreshold)
          blackPixelCount++;
      }
      if (blackPixelCount >= 1500) {
        bottom = y;
        break;
      }
    }

    // Detect left border
    for (let x = 0; x < width; x++) {
      let blackPixelCount = 0;
      for (let y = 0; y < height; y++) {
        const index = (y * width + x) * 3;
        const [r, g, b] = [data[index], data[index + 1], data[index + 2]];
        if (r < blackThreshold && g < blackThreshold && b < blackThreshold)
          blackPixelCount++;
      }

      if (blackPixelCount >= 2600) {
        left = x;
        break;
      }
    }

    // Detect right border
    for (let x = width; x >= 0; x--) {
      let blackPixelCount = 0;
      for (let y = 0; y < height; y++) {
        const index = (y * width + x) * 3;
        const [r, g, b] = [data[index], data[index + 1], data[index + 2]];
        if (r < blackThreshold && g < blackThreshold && b < blackThreshold)
          blackPixelCount++;
      }
      if (blackPixelCount >= 2600) {
        right = x;
        break;
      }
    }

    // Define the crop area based on detected bounding box
    const cropWidth = right - left;
    const cropHeight = bottom - top;

    if (cropWidth > 0 && cropHeight > 0) {
      console.log('Successfully crop image using border detection.');
      return await sharp(fileBuffer)
        .extract({
          left,
          top,
          width: Math.min(cropWidth, 1926),
          height: Math.min(cropHeight, 2866),
        })
        .toBuffer();
    } else {
      console.log(
        'Could not detect a valid character border or image is too small.',
      );
      return null;
    }
  }

  private async processRawImage(fileBuffer: Buffer) {
    try {
      const rawFileBuffer = fileBuffer;
      let scanResult = await this.scanQRCode(fileBuffer);

      if (scanResult.data.split('-')[1] === 'b') {
        fileBuffer = await sharp(fileBuffer).rotate(180).toBuffer();
        scanResult = await this.scanQRCode(fileBuffer);
      }

      const characterName = scanResult.data.split('-')[0];

      const borderDetectionCropped = await this.borderDetectionCrop(fileBuffer);

      if (borderDetectionCropped) {
        fileBuffer = borderDetectionCropped;
      } else {
        // all these number are for 300DPI scanned paper image (2480x3507)
        fileBuffer = await sharp(fileBuffer)
          .extract({
            left: Math.round(scanResult.location.topRightCorner.x + 22), // this magic number is the distance from the topRightCorner of the QR code dot to the left border of the coloring frame (decent number)
            top: Math.round(scanResult.location.topRightCorner.y + 199), // this magic number is the distance from the topRightCorner of the QR code dot to the top border of the coloring frame (decent number)
            width: 1926, // width and height of the frame in the actual paper
            height: 2866,
          })
          .toBuffer();
      }

      // DEBUG cropped frame
      // await sharp(fileBuffer).toFile(
      //   join(process.cwd(), 'public', 'physical', 'test.png'),
      // );

      return {
        characterName,
        fileBuffer,
        rawFileBuffer,
        templateRotation: parseInt(scanResult.data.split('-')[2]),
      };
    } catch (error) {
      this.logger.error('Error while processing raw image:', error);
      throw new InternalServerErrorException();
    }
  }

  private async storeRawFile({
    id,
    characterName,
    rawFileBuffer,
    fileBuffer,
  }: {
    id: string;
    characterName: string;
    rawFileBuffer: Buffer;
    fileBuffer: Buffer;
  }) {
    const storagePath = join(
      process.cwd(),
      'public',
      'physical',
      'customized',
      characterName,
      dateWithTz().format('YYYY-MM-DD'),
      id,
    );

    if (!existsSync(storagePath)) {
      mkdirSync(storagePath, { recursive: true });
    }

    const characterAreaFilePath = join(storagePath, 'cropped.png');
    await sharp(fileBuffer).toFile(characterAreaFilePath);

    const rawFilePath = join(storagePath, 'raw.png');
    await sharp(rawFileBuffer).toFile(rawFilePath);

    return { id, characterName, characterAreaFilePath };
  }

  private async loadCharacterBaseFile(characterName: string) {
    const physicalPath = join(process.cwd(), 'public', 'physical');
    const animationPath = join(physicalPath, 'animation', characterName);
    const extractionPath = join(physicalPath, 'extraction', characterName);

    if (!existsSync(animationPath) || !existsSync(extractionPath)) {
      throw new NotFoundException("Can't find the character base assets.");
    }

    const jsonPath = join(extractionPath, `${characterName}.json`);
    const characterJson: CharacterJSON = JSON.parse(
      readFileSync(jsonPath, 'utf-8'),
    );

    const atlasPath = join(extractionPath, `${characterName}.atlas`);
    const characterAtlas = readFileSync(atlasPath, 'utf8').split('\n');

    const imagePath = join(extractionPath, `${characterName}.png`);
    const characterBaseImage = await loadImage(imagePath);

    return {
      json: characterJson,
      jsonPath,
      animationJson: join(animationPath, `${characterName}.json`),
      atlas: characterAtlas,
      atlasPath,
      animationAtlas: join(animationPath, `${characterName}.atlas`),
      image: characterBaseImage,
    };
  }

  private async generateAssetFromRawFile({
    id,
    characterName,
    characterAreaFilePath,
    templateRotation,
  }: {
    id: string;
    characterName: string;
    characterAreaFilePath: string;
    templateRotation: number;
  }) {
    return new Promise(async (resolve) => {
      const baseAssets = await this.loadCharacterBaseFile(characterName);

      const characterPaperSize: CharacterPaperSize = {
        width: 1668, //Magic number for 300DPI paper
        height: Math.round(
          (1668 / baseAssets.json.skeleton.width) *
            baseAssets.json.skeleton.height,
        ),
      };

      const ratio = characterPaperSize.width / baseAssets.json.skeleton.width;

      const extractableAttachment = Object.keys(
        baseAssets.json.skins[0].attachments,
      ).filter((attachment) => attachment.includes('white'));

      const pngSize = baseAssets.atlas
        .find((item) => item.includes('size'))
        .replace('size:', '')
        .split(',');
      const [pngWidth, pngHeight] = pngSize.map((n) => parseInt(n));

      const attachmentDetails = extractableAttachment.map((attachmentName) => {
        const characterAttachmentIndexInAtlas =
          baseAssets.atlas.indexOf(attachmentName);

        const rawBounds = baseAssets.atlas[characterAttachmentIndexInAtlas + 1];

        if (!rawBounds.includes('bounds')) {
          throw new NotFoundException(
            `Can't find ${attachmentName} bounds info for ${characterName}`,
          );
        }

        const [x, y, width, height] = rawBounds
          .replace('bounds:', '')
          .split(',')
          .map((n) => parseInt(n));

        const rawRotate = baseAssets.atlas[characterAttachmentIndexInAtlas + 2];
        const rotate = rawRotate.includes('rotate')
          ? parseInt(rawRotate.split('rotate:')[1])
          : 0;

        return {
          name: attachmentName,
          x,
          y,
          width,
          height,
          rotate,
        };
      });

      const canvas = createCanvas(pngWidth, pngHeight);
      const ctx = canvas.getContext('2d');
      ctx.drawImage(baseAssets.image, 0, 0, pngWidth, pngHeight);
      ctx.imageSmoothingEnabled = false;

      const rawImage = await loadImage(characterAreaFilePath);

      for await (const {
        name,
        x,
        y,
        width,
        height,
        rotate,
      } of attachmentDetails) {
        const attachment = baseAssets.json.skins[0].attachments[name][name];

        const shiftedToCanvasVertices = this.shiftVerticesToCanvas({
          attachment,
          ratio,
          skeleton: baseAssets.json.skeleton,
        });

        const croppedPartBuffer = this.cropImage({
          rawImage,
          vertices: shiftedToCanvasVertices,
          characterPaperSize,
        });

        const allXVertices = shiftedToCanvasVertices.map((v) => v[0]);
        const allYVertices = shiftedToCanvasVertices.map((v) => v[1]);

        const minX = Math.ceil(Math.min(...allXVertices));
        const minY = Math.ceil(Math.min(...allYVertices));

        const maxX = Math.ceil(Math.max(...allXVertices));
        const maxY = Math.ceil(Math.max(...allYVertices));

        // DEBUG Cropped part
        // console.log(name, {
        //   left: minX,
        //   top: minY,
        //   width: maxX - minX,
        //   height: maxY - minY,
        // });
        // await sharp(croppedPartBuffer)
        //   .extract({
        //     left: minX,
        //     top: minY,
        //     width: maxX - minX,
        //     height: maxY - minY,
        //   })
        //   .rotate(-rotate - templateRotation)
        //   .toFile(
        //     join(process.cwd(), 'public', 'physical', 'test', `${name}.png`),
        //   );

        const croppedPart = await sharp(croppedPartBuffer)
          .extract({
            left: minX,
            top: minY,
            width: maxX - minX,
            height: maxY - minY,
          })
          .rotate(-rotate - templateRotation) // if paper layout is landscape need to - 90
          .toBuffer();

        ctx.drawImage(
          await loadImage(croppedPart),
          x,
          y,
          rotate === 90 ? height : width,
          rotate === 90 ? width : height,
        );
      }

      const outputFolder = join(
        process.cwd(),
        'public',
        'physical',
        'customized',
        characterName,
        dateWithTz().format('YYYY-MM-DD'),
        id,
      );

      const outputJsonPath = join(outputFolder, `${characterName}.json`);
      const outputAtlasPath = join(outputFolder, `${characterName}.atlas`);
      const outputImagePath = join(outputFolder, `${characterName}.png`);

      copyFileSync(baseAssets.animationJson, outputJsonPath);
      copyFileSync(baseAssets.animationAtlas, outputAtlasPath);
      sharp(canvas.toBuffer('image/png')).toFile(
        outputImagePath,
        (error, info) => {
          if (error) {
            this.logger.error(error);
          }

          this.logger.log(
            `[${id}] Generated cropped image info: ${JSON.stringify(info)}`,
          );
          resolve({ id, characterName });
        },
      );
    });
  }

  private shiftVerticesToCanvas({
    attachment,
    ratio,
    skeleton,
  }: {
    attachment: Attachment;
    ratio: number;
    skeleton: Skeleton;
  }) {
    const shiftedVertices = [];

    for (let index = 0; index < attachment.vertices.length; index += 2) {
      const x = attachment.vertices[index];
      const y = attachment.vertices[index + 1];

      const shiftedX = (x + skeleton.width / 2) * ratio;
      const shiftedY = (-y + skeleton.height + skeleton.y) * ratio;

      shiftedVertices.push([Math.abs(shiftedX), Math.abs(shiftedY)]);
    }

    return shiftedVertices;
  }

  private cropImage({
    rawImage,
    vertices,
    characterPaperSize: { width, height },
  }: {
    rawImage: Image;
    vertices: number[][];
    characterPaperSize: CharacterPaperSize;
  }) {
    // Create a new canvas for the cropped image
    // buffer 10px for extraction
    const canvas = createCanvas(width + 10, height + 10);
    const ctx = canvas.getContext('2d');

    ctx.save();
    ctx.beginPath();

    ctx.moveTo(vertices[0][0], vertices[0][1]);
    for (const vertex of vertices) {
      ctx.lineTo(vertex[0], vertex[1]);
    }

    ctx.closePath();
    ctx.clip();

    ctx.drawImage(
      rawImage,
      (width - rawImage.width) / 2,
      (height - rawImage.height) / 2,
    );

    ctx.restore();

    return canvas.toBuffer('image/png');
  }
}
