import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Role, User } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { randomUUID } from 'crypto';
import { CustomPrismaService } from 'nestjs-prisma';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { UserService } from 'src/user/user.service';
import {
  ExternalAuthorizeResponse,
  ExternalJwt,
  JwtPayload,
  SamlProfile,
  ValidatedUserAfterLocalAuth,
  ValidatedUserAfterSamlAuth,
} from './auth.type';
import { GenerateApiKeyDto } from './dto/generate-api-key.dto';
import {
  NLBCASAuthorizeDto,
  NLBCASAuthorizeDtoFake,
} from './dto/nlb-cas-authorize.dto';
import { RegisterDto } from './dto/register.dto';
import { RevokeApiKeyDto } from './dto/revoke-api-key.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
  ) {}

  async exchangeApiKeyForAppId(key: string | null | undefined) {
    try {
      const { appId } = await this.prisma.client.apiKey.findUniqueOrThrow({
        where: {
          key,
        },
        select: {
          appId: true,
        },
      });

      return appId;
    } catch (error) {
      this.logger.error(error.message);
      return null;
    }
  }

  async generateApiKey({ appId }: GenerateApiKeyDto) {
    return await this.prisma.client.apiKey.create({
      data: {
        key: bcrypt.hashSync(randomUUID(), 12).slice(9),
        app: {
          connectOrCreate: {
            where: { id: appId },
            create: {
              id: appId,
              setting: { create: {} },
            },
          },
        },
      },
      select: { key: true },
    });
  }

  async revokeApiKey({ appId }: RevokeApiKeyDto) {
    await this.prisma.client.apiKey.delete({
      where: { appId },
    });

    return `ApiKey for ${appId} revoked!`;
  }

  async register({ username, password }: RegisterDto) {
    try {
      const hashedPassword = await bcrypt.hash(password, 12);

      await this.userService.create({
        username,
        password: hashedPassword,
        role: Role.USER,
      });

      return `User ${username} registered!`;
    } catch (error) {
      if (error.code === 'P2002') {
        throw new ConflictException(
          'Invalid username! Please try another username.',
        );
      }
    }
  }

  async signin(user: ValidatedUserAfterLocalAuth, name?: string) {
    const tokens = this.generateToken(user, name);

    const decodedAT = this.jwtService.decode(tokens.accessToken);

    await this.userService.update(user.id, {
      lastTokenIat: decodedAT.iat,
    });

    return tokens;
  }

  async nlbCasAuthorize(dto: NLBCASAuthorizeDto) {
    const base64Token = Buffer.from(
      `${process.env.NLB_OIDC_CLIENT_ID}:${process.env.NLB_OIDC_CLIENT_SECRET}`,
    ).toString('base64');

    const response = await fetch(
      `${process.env.NLB_OIDC_URL}/accessToken?` +
        new URLSearchParams({
          code: dto.code,
          grant_type: 'authorization_code',
          redirect_uri: process.env.NLB_OIDC_REDIRECT_URI,
        }),
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Basic ${base64Token}`,
        },
      },
    );

    if (response.status !== 200) {
      throw new UnauthorizedException('Invalid code!');
    }

    const data = (await response.json()) as ExternalAuthorizeResponse;

    // const data: ExternalAuthorizeResponse = {
    //   access_token:
    //     'eyJhbGciOiJSUzUxMiIsInR5cCI6IkpXVCIsIm9yZy5hcGVyZW8uY2FzLnNlcnZpY2VzLlJlZ2lzdGVyZWRTZXJ2aWNlIjoiMzgiLCJraWQiOiJubGItZGV2LXNpZyJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JnisHkaorvha297t1d6Cdr3cdrj__vslLcDbww-okkLw354gWDHlinUQSa5ZM6DTl3m_t4jhlNxNPmdosnOTUCdptyHVAd2C8TYBr2ZWIW_YYW8e489DVilsbKIxeYL5gskPAkZKe_R8a6x6jI9_HcD5cVu--cj5tLb2HF0o5JFxa1DQWZm4sS3GxSX4HWuHgpc5OFOMonV4-y-gpoVy5DfwvdLvqJ0UBVi4bq5MhDGqUUh7jo4rm_3-Te8JWnUtRtbZ70R-PiwUJnBwbMEBjD8RD0kmgBjm7iUZ3C6OBQWxH5PAssqcAor3yiRR87a1gvCzQr64aLIoR3aVng2v8A',

    //   id_token:
    //     'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Im5sYi1kZXYtc2lnIn0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_CIdEm_foSNTsKFKKlcy0uCOlosH723ll-0HUiq6LSRls2_QqxZejNv1ccE3NTw_N9SHiDDrsI4OaCPFJnDNleG8T6YKTbZNSywVClQh1cd3KcjW5j3uCyV_US0qeM3e4fCgg6QpHw_RkHRwRYIHzJCodstdNatTHY_xLvBSUTksgqUYmsCyKL265JwztQ9GfHEUu2x1LmMkjHDS_SxIRC0OFGou8li64-l2VyuB5DXjqKAnFQrVBYDP8zftxU0ukMg-W17aCYgV0og49mHAa9mqneij7XlBGKlA7uy2QbUKDJwdKDeXN69P87CRcM_mBBjiRJHHGoCMPYNfAZysA',

    //   token_type: 'bearer',

    //   expires_in: 28800,

    //   scope: 'nlb_scope openid nlb_profile',
    // };

    const parsedJwt = this.parseExternalJwt(data.id_token);
    this.validateExternalJwt(parsedJwt);

    const user = await this.userService.findOneByUsername(
      parsedJwt.pdvId.toString(),
    );

    if (!user) {
      const newUser = await this.userService.create({
        username: `${parsedJwt.pdvId}`,
        role: Role.EXTERNAL_USER,
      });

      await this.prisma.client.session.create({
        data: {
          token: data.id_token,
          user: { connect: { id: newUser.id } },
          expiredAt: new Date(Date.now() + data.expires_in * 1000),
        },
      });

      return this.signin(newUser, parsedJwt.name);
    }

    if (user.deactivatedAt) {
      throw new UnauthorizedException('User deactivated!');
    }

    await this.prisma.client.session.upsert({
      where: {
        userId: user.id,
      },
      update: {
        token: data.id_token,
        expiredAt: new Date(Date.now() + data.expires_in * 1000),
      },
      create: {
        token: data.id_token,
        user: { connect: { id: user.id } },
        expiredAt: new Date(Date.now() + data.expires_in * 1000),
      },
    });

    return this.signin(user, parsedJwt.name);
  }

  async nlbCasAuthorizeFake(dto: NLBCASAuthorizeDtoFake) {
    const user = await this.userService.findOneByUsername(dto.pdvId);

    if (!user) {
      await this.userService.create({
        username: dto.pdvId,
        role: Role.EXTERNAL_USER,
      });
    }

    if (user.deactivatedAt) {
      throw new UnauthorizedException('User deactivated!');
    }

    const expiredAt24Hours = new Date(Date.now() + 24 * 3600 * 1000);

    await this.prisma.client.session.upsert({
      where: {
        userId: user.id,
      },
      update: {
        token: user.id,
        expiredAt: expiredAt24Hours,
      },
      create: {
        token: user.id,
        user: { connect: { id: user.id } },
        expiredAt: expiredAt24Hours,
      },
    });

    return this.signin(user);
  }

  async validateLocalUser(username: string, password: string): Promise<User> {
    const user = await this.userService.findOneByUsername(username);

    if (!user) {
      throw new BadRequestException('Invalid credentials!');
    }

    const passwordMatch = await bcrypt.compare(password, user.password);

    if (!passwordMatch) {
      throw new BadRequestException(`Invalid credentials`);
    }

    user.password = undefined;

    return user;
  }

  async validateJwtToken(jwtUser: JwtPayload) {
    const user = await this.userService.findOne(jwtUser.sub);

    if (user?.lastTokenIat !== jwtUser.iat) {
      throw new UnauthorizedException('Invalid Token!');
    }

    if (user.deactivatedAt) {
      throw new UnauthorizedException('User deactivated!');
    }
  }

  async validateExternalSession(userId: string) {
    const session = await this.prisma.client.session.findFirst({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });

    if (!session) {
      throw new UnauthorizedException('Invalid session!');
    }

    return session.token;
  }

  async signout(userId: string) {
    await this.userService.update(userId, {
      lastTokenIat: null,
    });
  }

  parseExternalJwt(token: string): ExternalJwt {
    return JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
  }

  validateExternalJwt(jwt: ExternalJwt) {
    if (
      !jwt.iss.startsWith(process.env.NLB_OIDC_URL) ||
      jwt.aud !== process.env.NLB_OIDC_CLIENT_ID ||
      jwt.client_id !== process.env.NLB_OIDC_CLIENT_ID
    ) {
      throw new UnauthorizedException('Invalid token!');
    }

    if (
      jwt.exp <
      Date.now() / 1000
      // jwt.auth_time < Date.now() / 1000 - 180
    ) {
      throw new UnauthorizedException('Token expired!');
    }
  }

  async validateSamlUser(
    profile: SamlProfile,
  ): Promise<ValidatedUserAfterSamlAuth> {
    this.logger.log(`SAML user login attempt: ${profile.nameID}`);

    // Use nameID as the unique identifier for SAML users
    const username = profile.nameID;

    let user = await this.userService.findOneByUsername(username);

    if (!user) {
      // Create new user for first-time SAML login
      const newUser = await this.userService.create({
        username,
        displayName:
          profile['http://schemas.microsoft.com/identity/claims/displayname'] ||
          username,
        role: Role.EXTERNAL_USER,
        // You might want to store additional profile info if your User model supports it
      });

      user = await this.userService.findOne(newUser.id);

      this.logger.log(`Created new SAML user: ${username}`);
    }

    if (user.deactivatedAt) {
      throw new UnauthorizedException('User deactivated!');
    }

    // Store SAML session information (optional, similar to NLB CAS)
    const expiredAt24Hours = new Date(Date.now() + 24 * 3600 * 1000);

    await this.prisma.client.session.upsert({
      where: {
        userId: user.id,
      },
      update: {
        token: `saml_${user.id}`, // Simple token for SAML sessions
        expiredAt: expiredAt24Hours,
      },
      create: {
        token: `saml_${user.id}`,
        user: { connect: { id: user.id } },
        expiredAt: expiredAt24Hours,
      },
    });

    return {
      id: user.id,
      username: user.username,
      role: user.role,
      samlProfile: profile,
    };
  }

  async samlLogin(user: ValidatedUserAfterSamlAuth) {
    // Extract display name from SAML profile for the JWT token
    const displayName =
      user.samlProfile[
        'http://schemas.microsoft.com/identity/claims/displayname'
      ] || user.username;

    return this.signin(user, displayName);
  }

  private generateToken(user: ValidatedUserAfterLocalAuth, name?: string) {
    const payload = {
      sub: user.id,
      pdvId: user.username,
      role: user.role,
      name,
    };

    return {
      accessToken: this.jwtService.sign(payload),
    };
  }
}
