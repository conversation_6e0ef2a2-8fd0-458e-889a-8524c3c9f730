import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiTags } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { UserFromJwt, ValidatedUserAfterLocalAuth } from './auth.type';
import { User } from './decorators/params.decorator';
import { Roles } from './decorators/role.decorator';
import { GenerateApiKeyDto } from './dto/generate-api-key.dto';
import { LoginDto } from './dto/login.dto';
import {
  NLBCASAuthorizeDto,
  NLBCASAuthorizeDtoFake,
} from './dto/nlb-cas-authorize.dto';
import { RegisterDto } from './dto/register.dto';
import { RevokeApiKeyDto } from './dto/revoke-api-key.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { RolesGuard } from './guards/role.guard';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @Post('generate-api-key')
  async generateApiKey(@Body() dto: GenerateApiKeyDto) {
    return this.authService.generateApiKey(dto);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @Post('revoke-api-key')
  async revokeApiKey(@Body() dto: RevokeApiKeyDto) {
    return this.authService.revokeApiKey(dto);
  }

  @Post('sign-up')
  async register(@Body() dto: RegisterDto) {
    return this.authService.register(dto);
  }

  @Post('sign-in')
  @UseGuards(LocalAuthGuard)
  @ApiBody({ type: LoginDto })
  async signin(@User() user: ValidatedUserAfterLocalAuth) {
    return this.authService.signin(user);
  }

  @Post('nlb-cas/authorize')
  async nlbCasAuthorize(@Body() dto: NLBCASAuthorizeDto) {
    return this.authService.nlbCasAuthorize(dto);
  }

  @Post('sign-in-fake')
  @UseGuards(LocalAuthGuard)
  @ApiBody({ type: LoginDto })
  async signinFake(@User() user: ValidatedUserAfterLocalAuth) {
    return this.authService.signin(user);
  }

  @Post('nlb-cas/authorize-fake')
  async nlbCasAuthorizeFake(@Body() dto: NLBCASAuthorizeDtoFake) {
    return this.authService.nlbCasAuthorizeFake(dto);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'EXTERNAL_USER', 'USER', 'SUPERADMIN')
  @Post('sign-out')
  async signout(@User() user: UserFromJwt) {
    return this.authService.signout(user.sub);
  }
}
