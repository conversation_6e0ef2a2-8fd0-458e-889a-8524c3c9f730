import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-saml';
import { readFileSync } from 'fs';
import { join } from 'path';
import { AuthService } from '../auth.service';
import { SamlProfile, ValidatedUserAfterSamlAuth } from '../auth.type';

@Injectable()
export class SamlStrategy extends PassportStrategy(Strategy, 'saml') {
  constructor(private authService: AuthService) {
    // Read the certificate file
    const certPath = join(process.cwd(), 'certs', 'Nlb-KidSpot-CMS-Prod.cer');
    const cert = readFileSync(certPath, 'utf8');

    super({
      // Service Provider (our app) configuration
      callbackUrl:
        process.env.SAML_CALLBACK_URL ||
        'http://localhost:3000/auth/saml/callback',
      entryPoint:
        process.env.SAML_IDP_SSO_URL ||
        'https://login.microsoftonline.com/0b11c524-9a1c-4e1b-84cb-6336aefc2243/saml2',
      issuer: process.env.SAML_ENTITY_ID || 'nlb-kidspot-cms',

      // Identity Provider configuration
      cert: cert,

      // SAML protocol settings
      identifierFormat: 'urn:oasis:names:tc:SAML:2.0:nameid-format:persistent',
      signatureAlgorithm: 'sha256',
      digestAlgorithm: 'sha256',

      // Security settings
      validateInResponseTo: false,
      disableRequestedAuthnContext: true,

      // Attribute mapping
      authnContext: [
        'http://schemas.microsoft.com/ws/2008/06/identity/authenticationmethod/password',
      ],

      // Additional settings
      skipRequestCompression: false,
    });
  }

  async validate(profile: SamlProfile): Promise<ValidatedUserAfterSamlAuth> {
    const user = await this.authService.validateSamlUser(profile);

    return {
      id: user.id,
      username: user.username,
      role: user.role,
      samlProfile: user.samlProfile,
    };
  }
}
