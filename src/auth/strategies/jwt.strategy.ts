import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from '../auth.service';
import { JwtPayload, UserFromJwt } from '../auth.type';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET,
    });
  }

  async validate(payload: JwtPayload): Promise<UserFromJwt> {
    await this.authService.validateJwtToken(payload);
    
    if (payload.role === 'EXTERNAL_USER') {
      const externalToken = await this.authService.validateExternalSession(
        payload.sub,
      );
      const parsedToken = this.authService.parseExternalJwt(externalToken);

      return { ...payload, pdvId: parsedToken.pdvId };
    } else {
      return { ...payload, pdvId: null };
    }
  }
}
