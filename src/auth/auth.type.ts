import { Role, User } from '@prisma/client';

export type ValidatedUserAfterLocalAuth = Pick<
  User,
  'id' | 'username' | 'role'
>;

export type JwtPayload = {
  sub: string;
  pdvId: number;
  role: Role;
  iat: number;
  exp: number;
};

export type ExternalAuthorizeResponse = {
  access_token: string;
  id_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
};

export type ExternalJwt = {
  jti: string;
  sid: string;
  iss: string;
  aud: string;
  exp: number;
  iat: number;
  nbf: number;
  sub: string;
  client_id: string;
  auth_time: number;
  state: string;
  nonce: string;
  at_hash: string;
  email: string;
  family_name: string;
  first_name: string;
  given_name: string;
  mId: string;
  mylibraryId: string;
  name: string;
  pdvId: number;
  preferred_name: string;
  preferred_username: string;
};

export type UserFromJwt = JwtPayload & Pick<ExternalJwt, 'pdvId'>;
