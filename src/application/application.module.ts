import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { ApplicationController } from './application.controller';
import { ApplicationService } from './application.service';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'projection-queue',
    }),
    BullModule.registerQueue({
      name: 'projection-setting-queue',
    }),
  ],
  controllers: [ApplicationController],
  providers: [ApplicationService],
  exports: [ApplicationService],
})
export class ApplicationModule {}
