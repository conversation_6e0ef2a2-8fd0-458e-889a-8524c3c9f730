import { InjectQueue } from '@nestjs/bull';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import { CustomPrismaService } from 'nestjs-prisma';
import { dateWithTz } from 'src/libs/datetime';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { ProjectionSettingJobData } from 'src/projection/projection-setting.processor';
import { CharacterJobData } from 'src/projection/projection.processor';
import { CreateApplicationDto } from './dto/create-application.dto';
import {
  UpdateApplicationDto,
  UpdateApplicationSettingsDto,
} from './dto/update-application.dto';
import { PageNumberPaginationOptions } from 'prisma-extension-pagination';
import { MediaType } from '@prisma/client';

@Injectable()
export class ApplicationService {
  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    @InjectQueue('projection-queue')
    private projectionQueue: Queue<CharacterJobData>,
    @InjectQueue('projection-setting-queue')
    private projectionSettingQueue: Queue<ProjectionSettingJobData>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async create(dto: CreateApplicationDto) {
    return await this.prisma.client.application.create({
      data: {
        id: dto.id,
        name: dto.name,
        setting: { create: {} },
      },
      select: { id: true },
    });
  }

  async findAll(paginateParams: PageNumberPaginationOptions) {
    return await this.prisma.client.application.paginate().withPages({
      ...paginateParams,
      includePageCount: true,
    });
  }

  async findOne(id: string) {
    return await this.prisma.client.application.findUniqueOrThrow({
      where: { id },
    });
  }

  async findById(id: string) {
    const application = await this.prisma.client.application.findUnique({
      where: { id },
    });

    if (!application) {
      throw new BadRequestException('Invalid Library');
    }

    return application;
  }

  async update(id: string, dto: UpdateApplicationDto) {
    return await this.prisma.client.application.update({
      where: { id },
      data: dto,
      select: { id: true },
    });
  }

  async findOneSetting(appId: string) {
    const cacheKey = `setting:${appId}`;

    const appSettingFromCache: typeof fullSetting =
      await this.cacheManager.get(cacheKey);

    if (appSettingFromCache) return appSettingFromCache;

    const setting = await this.prisma.client.setting.findUniqueOrThrow({
      where: { appId },
    });

    let background: {
      id: string;
      name: string;
      type: MediaType;
      path: string;
    } | null = null;

    if (setting.backgroundId) {
      background = await this.prisma.client.media.findUnique({
        where: { id: setting.backgroundId },
        select: {
          id: true,
          name: true,
          path: true,
          type: true,
        },
      });
    }

    //TODO: move to individual character setting
    const characterSetting = await this.prisma.client.character.findFirst({
      where: { size: { gt: 0 } },
      select: { size: true, speed: true },
    });

    const fullSetting = Object.assign(setting, characterSetting, {
      background,
    });

    await this.cacheManager.set(cacheKey, fullSetting, 0);

    return fullSetting;
  }

  async updateSetting(
    appId: string,
    { size, speed, ...restDto }: UpdateApplicationSettingsDto,
  ) {
    const cacheKey = `setting:${appId}`;

    const updatedSetting = await this.prisma.client.setting.update({
      where: { appId },
      data: restDto,
    });

    let background: {
      id: string;
      name: string;
      type: MediaType;
      path: string;
    } | null = null;

    if (updatedSetting.backgroundId) {
      background = await this.prisma.client.media.findUnique({
        where: { id: updatedSetting.backgroundId },
        select: {
          id: true,
          name: true,
          path: true,
          type: true,
        },
      });
    }

    await this.prisma.client.character.updateMany({
      where: { size: { gt: 0 } },
      data: { size, speed },
    });

    //TODO: move to individual character setting
    const fullSetting = Object.assign(
      updatedSetting,
      {
        size,
        speed,
      },
      { background },
    );

    await this.cacheManager.set(cacheKey, fullSetting, 0);

    await this.updateProjectionWall(appId, fullSetting);

    return fullSetting;
  }

  private async updateProjectionWall(
    appId: string,
    {
      background,
      maxCharacters,
      size,
      speed,
    }: Awaited<ReturnType<typeof this.updateSetting>>,
  ) {
    const characters = await this.prisma.client.projection.findMany({
      where: { appId, enteredAt: { not: null }, exitedAt: null },
      orderBy: { enteredAt: 'asc' },
    });

    const diff = characters.length - maxCharacters;

    if (diff > 0) {
      const shouldExitCharacters = characters.splice(0, diff);

      shouldExitCharacters.forEach((character) => {
        this.projectionSettingQueue.add(
          'exit-character',
          { appId, characterId: character.id },
          { removeOnComplete: true },
        );
      });
    } else {
      await this.projectionQueue.resume();
    }

    characters.forEach((character) => {
      this.projectionSettingQueue.add(
        'resize-character',
        { appId, characterId: character.id, size },
        { removeOnComplete: true },
      );

      this.projectionSettingQueue.add(
        'speed-character',
        { appId, characterId: character.id, speed },
        { removeOnComplete: true },
      );
    });

    if (background) {
      this.projectionSettingQueue.add(
        'change-background',
        { appId, backgroundPath: background.path },
        { removeOnComplete: true },
      );
    }
  }

  async projectionStatus(appId: string) {
    const { id } = await this.findOne(appId);

    const projectedCharacters = await this.prisma.client.projection.findMany({
      where: { appId: id },
    });

    return {
      queueLength: (await this.projectionQueue.getJobs(['paused'])).filter(
        ({ data }) => data.appId === id,
      ).length,
      totalCharacters: projectedCharacters.length,
      activeCharacters: projectedCharacters.filter(
        ({ enteredAt, exitedAt, exiting }) =>
          enteredAt && !exitedAt && !exiting,
      ).length,
    };
  }

  async flushCharacter(appId: string) {
    const { id } = await this.findOne(appId);

    await this.prisma.client.projection.updateMany({
      where: {
        appId: id,
        enteredAt: { not: null },
        exitedAt: null,
      },
      data: { exitedAt: dateWithTz().toDate(), exiting: false, waiting: false },
    });

    return await this.projectionStatus(appId);
  }

  async flushQueue(appId: string) {
    const { id } = await this.findOne(appId);

    (await this.projectionQueue.getJobs(['paused'])).forEach(async (job) => {
      if (job.data.appId === id) {
        await job?.remove();
      }
    });

    return await this.projectionStatus(appId);
  }

  async exchangeQrCodeForAppToken(qrCode: string) {
    const qrCodeData = await this.prisma.client.qrCode.findUnique({
      where: { code: qrCode },
    });

    if (!qrCodeData) {
      throw new BadRequestException('Invalid QR Code');
    }

    if (qrCodeData.expiredAt < new Date()) {
      throw new BadRequestException('Expired QR Code');
    }

    const appToken = await this.prisma.client.apiKey.findUnique({
      where: {
        appId: qrCodeData.appId,
      },
      select: {
        app: {
          select: { name: true },
        },
        key: true,
      },
    });

    if (!appToken) {
      throw new BadRequestException('Invalid App');
    }

    return {
      appName: appToken.app.name,
      appToken: appToken.key,
    };
  }
}
