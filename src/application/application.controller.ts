import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import * as crypto from 'crypto';
import { Request, Response } from 'express';
import { Public } from 'src/auth/decorators/auth.decorator';
import { Roles } from 'src/auth/decorators/role.decorator';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { ApplicationService } from './application.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import {
  UpdateApplicationDto,
  UpdateApplicationSettingsDto,
} from './dto/update-application.dto';
import { sleep } from 'src/libs/datetime';

@ApiTags('Application')
@Controller('application')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class ApplicationController {
  constructor(private readonly applicationService: ApplicationService) {}

  @Post()
  @Roles('ADMIN')
  create(@Body() createApplicationDto: CreateApplicationDto) {
    return this.applicationService.create(createApplicationDto);
  }

  @Get()
  @Roles('ADMIN')
  @ApiQuery({ name: 'page', example: 1, required: true })
  @ApiQuery({ name: 'limit', example: 10, required: true })
  findAll(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
  ) {
    return this.applicationService.findAll({ page, limit });
  }

  @Get(':id')
  @Roles('ADMIN')
  findOne(@Param('id') id: string) {
    return this.applicationService.findOne(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  update(@Param('id') id: string, @Body() dto: UpdateApplicationDto) {
    return this.applicationService.update(id, dto);
  }

  @Get(':id/settings')
  @Roles('ADMIN')
  findOneSettings(@Param('id') appId: string) {
    return this.applicationService.findOneSetting(appId);
  }

  @Patch(':id/settings')
  @Roles('ADMIN')
  updateSettings(
    @Param('id') appId: string,
    @Body() dto: UpdateApplicationSettingsDto,
  ) {
    return this.applicationService.updateSetting(appId, dto);
  }

  @Get(':id/projection/status')
  @Roles('ADMIN')
  projectionStatus(@Param('id') appId: string) {
    return this.applicationService.projectionStatus(appId);
  }

  @Put(':id/characters/flush')
  @Roles('ADMIN')
  flushCharacter(@Param('id') appId: string) {
    return this.applicationService.flushCharacter(appId);
  }

  @Put(':id/queue/flush')
  @Roles('ADMIN')
  flushQueue(@Param('id') appId: string) {
    return this.applicationService.flushQueue(appId);
  }

  @Roles('EXTERNAL_USER', 'USER')
  @Get('qr-code-exchange/:qrCode')
  exchangeQrCodeForAppToken(@Param('qrCode') qrCode: string) {
    return this.applicationService.exchangeQrCodeForAppToken(qrCode);
  }

  @Public()
  @Get(`r/${process.env.REDIRECT_CODE}`)
  async getWebAppToken(@Req() req: Request, @Res() res: Response) {
    const interval = Math.floor(Date.now() / 30000);
    const hash = crypto
      .createHash('sha256')
      .update(interval.toString())
      .digest('hex');

    const referer = req.headers['referer'];
    const targetUrl = `${process.env.WEBAPP_URL}/${hash}`;

    res.setHeader('referer', referer ?? process.env.SERVER_URL);
    res.redirect(302, targetUrl);
  }
}
