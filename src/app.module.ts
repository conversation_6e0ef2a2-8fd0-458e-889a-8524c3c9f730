import { BullModule } from '@nestjs/bull';
import { CacheModule } from '@nestjs/cache-manager';
import { HttpStatus, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ServeStaticModule } from '@nestjs/serve-static';
import {
  CustomPrismaModule,
  providePrismaClientExceptionFilter,
} from 'nestjs-prisma';
import { join } from 'path';
import { ApplicationModule } from './application/application.module';
import { AuthModule } from './auth/auth.module';
import { CharacterModule } from './character/character.module';
import { ColoringModule } from './coloring/coloring.module';
import { HealthModule } from './health/health.module';
import { InteractionModule } from './interaction/interaction.module';
import { extendedPrismaClient } from './libs/prisma/prisma.extension';
import { ProjectionModule } from './projection/projection.module';
import { ReportModule } from './report/report.module';
import { RewardModule } from './reward/reward.module';
import { UploadModule } from './upload/upload.module';
import { UserModule } from './user/user.module';
import { UserCharacterModule } from './userCharacter/userCharacter.module';
import { ShopModule } from './shop/shop.module';
import { ScheduleModule } from '@nestjs/schedule';
import { FaqModule } from './faq/faq.module';

@Module({
  providers: [
    providePrismaClientExceptionFilter({
      P2000: HttpStatus.BAD_REQUEST,
      P2002: HttpStatus.CONFLICT,
      P2025: HttpStatus.NOT_FOUND,
      P2014: HttpStatus.UNPROCESSABLE_ENTITY,
    }),
  ],
  imports: [
    CustomPrismaModule.forRootAsync({
      isGlobal: true,
      name: 'PrismaService',
      useFactory: () => {
        return extendedPrismaClient;
      },
    }),
    ConfigModule.forRoot(),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..'),
      renderPath: 'public',
    }),
    CacheModule.register({
      isGlobal: true,
    }),
    BullModule.forRoot({
      redis: {
        host: 'localhost',
        port: 6379,
      },
    }),
    ScheduleModule.forRoot(),
    HealthModule,
    ApplicationModule,
    AuthModule,
    UserModule,
    RewardModule,
    InteractionModule,
    CharacterModule,
    ColoringModule,
    ProjectionModule,
    UserCharacterModule,
    ShopModule,
    ReportModule,
    UploadModule,
    FaqModule,
  ],
})
export class AppModule {}
