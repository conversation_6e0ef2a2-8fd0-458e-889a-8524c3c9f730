import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/role.decorator';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { CharacterService } from './character.service';
import { CreateCharacterDto } from './dto/create-character.dto';
import { CreateColorDto } from './dto/create-color.dto';
import { UpdateCharacterDto } from './dto/update-character.dto';
import { UpdateColorDto } from './dto/update-color.dto';
import { AssignColorToCharacterDto } from './dto/assign-color-to-character.dto';
import { UnassignColorFromCharacterDto } from './dto/unassign-color-from-character.dto';

@ApiTags('Character')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('character')
export class CharacterController {
  constructor(private readonly characterService: CharacterService) {}

  @Post('color')
  @Roles('ADMIN')
  createColor(@Body() dto: CreateColorDto) {
    return this.characterService.createColor(dto);
  }

  @Get('color')
  @Roles('ADMIN')
  @ApiQuery({ name: 'page', example: 1, required: true })
  @ApiQuery({ name: 'limit', example: 10, required: true })
  findColors(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
  ) {
    return this.characterService.findColors({ page, limit });
  }

  @Get('colors')
  @Roles('ADMIN', 'EXTERNAL_USER')
  findAllColors() {
    return this.characterService.findAllColors();
  }

  @Get('color/:id')
  @Roles('ADMIN')
  findOneColor(@Param('id') id: string) {
    return this.characterService.findOneColor(id);
  }

  @Patch('color/:id')
  @Roles('ADMIN')
  updateColor(@Param('id') id: string, @Body() dto: UpdateColorDto) {
    return this.characterService.updateColor(id, dto);
  }

  @Delete('color/:id')
  @Roles('ADMIN')
  removeColor(@Param('id') id: string) {
    return this.characterService.removeColor(id);
  }

  @Post('color/assign')
  @Roles('ADMIN')
  assignColorToCharacter(@Body() dto: AssignColorToCharacterDto) {
    return this.characterService.assignColorToCharacter(dto);
  }

  @Post('color/unassign')
  @Roles('ADMIN')
  unassignColorFromCharacter(@Body() dto: UnassignColorFromCharacterDto) {
    return this.characterService.unasignColorFromCharacter(dto);
  }

  @Post()
  @Roles('ADMIN')
  create(@Body() dto: CreateCharacterDto) {
    return this.characterService.create(dto);
  }

  @Get()
  @Roles('ADMIN')
  @ApiQuery({ name: 'page', example: 1, required: true })
  @ApiQuery({ name: 'limit', example: 10, required: true })
  findAll(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
  ) {
    return this.characterService.findAll({ page, limit });
  }

  @Get(':id')
  @Roles('ADMIN')
  findOne(@Param('id') id: string) {
    return this.characterService.findOne(id);
  }

  @Get(':id/colors')
  @Roles('ADMIN')
  findCharacterColors(@Param('id') id: string) {
    return this.characterService.findCharacterColors(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  update(@Param('id') id: string, @Body() dto: UpdateCharacterDto) {
    return this.characterService.update(id, dto);
  }

  @Delete(':id')
  @Roles('ADMIN')
  remove(@Param('id') id: string) {
    return this.characterService.remove(id);
  }
}
