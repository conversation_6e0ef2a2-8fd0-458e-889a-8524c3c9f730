import { Inject, Injectable, Logger } from '@nestjs/common';
import { CustomPrismaService } from 'nestjs-prisma';
import { PageNumberPaginationOptions } from 'prisma-extension-pagination';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { AssignColorToCharacterDto } from './dto/assign-color-to-character.dto';
import { CreateCharacterDto } from './dto/create-character.dto';
import { CreateColorDto } from './dto/create-color.dto';
import { UpdateCharacterDto } from './dto/update-character.dto';
import { UpdateColorDto } from './dto/update-color.dto';
import { UnassignColorFromCharacterDto } from './dto/unassign-color-from-character.dto';
import { ColorType } from '@prisma/client';

@Injectable()
export class CharacterService {
  private readonly logger = new Logger(CharacterService.name);

  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
  ) {}

  async create(dto: CreateCharacterDto) {
    const newCharacter = await this.prisma.client.character.create({
      data: { ...dto, name: dto.name.toLowerCase() },
    });

    return newCharacter;
  }

  async findAll(paginateParams: PageNumberPaginationOptions) {
    return this.prisma.client.character.paginate().withPages({
      ...paginateParams,
      includePageCount: true,
    });
  }

  async findOne(id: string) {
    return await this.prisma.client.character.findUniqueOrThrow({
      where: { id },
    });
  }

  async findCharacterColors(id: string) {
    const characterColors = await this.prisma.client.characterColor.findMany({
      where: { characterId: id },
    });

    const colors = await this.findAllColors();

    return {
      type: Object.keys(ColorType),
      colors,
      characterColors,
    };
  }

  async update(id: string, dto: UpdateCharacterDto) {
    const updatedCharacter = await this.prisma.client.character.update({
      where: { id },
      data: {
        ...dto,
        name: dto.name?.toLowerCase(),
      },
    });

    return updatedCharacter;
  }

  async remove(id: string) {
    const deletedCharacter = await this.prisma.client.character.delete({
      where: { id },
    });

    return deletedCharacter.name;
  }

  async createColor(dto: CreateColorDto) {
    const newColor = await this.prisma.client.color.create({
      data: dto,
    });

    return newColor;
  }

  async findColors(paginateParams: PageNumberPaginationOptions) {
    return this.prisma.client.color.paginate().withPages({
      ...paginateParams,
      includePageCount: true,
    });
  }

  async findAllColors() {
    return await this.prisma.client.color.findMany();
  }

  async findOneColor(id: string) {
    return await this.prisma.client.color.findUniqueOrThrow({
      where: { id },
    });
  }

  async updateColor(id: string, dto: UpdateColorDto) {
    const updatedColor = await this.prisma.client.color.update({
      where: { id },
      data: dto,
    });

    return updatedColor;
  }

  async removeColor(id: string) {
    await this.prisma.client.$transaction([
      this.prisma.client.characterColor.deleteMany({
        where: { colorId: id },
      }),
      this.prisma.client.color.delete({
        where: { id },
      }),
    ]);
  }

  async assignColorToCharacter({
    characterId,
    colorId,
    type,
  }: AssignColorToCharacterDto) {
    const assignedColor = await this.prisma.client.characterColor.upsert({
      where: {
        characterId_colorId: {
          characterId,
          colorId,
        },
      },
      create: {
        character: { connect: { id: characterId } },
        color: { connect: { id: colorId } },
        type,
      },
      update: {
        type,
      },
    });

    return assignedColor;
  }

  async unasignColorFromCharacter({
    characterId,
    colorId,
  }: UnassignColorFromCharacterDto) {
    await this.prisma.client.characterColor.delete({
      where: {
        characterId_colorId: {
          characterId,
          colorId,
        },
      },
    });
  }
}
