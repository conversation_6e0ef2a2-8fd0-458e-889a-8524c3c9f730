import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNumber,
  IsPositive,
  IsString,
  MaxLength,
  Min,
} from 'class-validator';

export class CreateCharacterDto {
  @ApiProperty({
    description: 'The name of the character',
    example: 'wintrix',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'The description of the character',
    example: 'The main character of the game',
  })
  @IsString()
  @MaxLength(50, { message: 'Description is too long' })
  description: string;

  @ApiProperty({
    description: 'Where character entering the screen',
    example: 'random',
  })
  @IsString()
  spawn: string;

  @ApiProperty({
    description: 'How character roaming around the screen',
    example: 'random',
  })
  @IsString()
  movement: string;

  @ApiProperty({
    description: 'Character initial face direction',
    example: 'left',
  })
  @IsString()
  faceDirection: string;

  @ApiProperty({
    description: "Character's price",
    example: 100,
  })
  @Min(0)
  @IsNumber()
  price: number;
}
