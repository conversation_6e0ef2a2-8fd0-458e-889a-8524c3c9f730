import { ApiProperty } from '@nestjs/swagger';
import { ColorType } from '@prisma/client';
import { IsEnum, IsString } from 'class-validator';

export class AssignColorToCharacterDto {
  @ApiProperty({
    description: 'Character ID to assign color to',
    example: 'random',
  })
  @IsString()
  characterId: string;

  @ApiProperty({
    description: 'Color ID to assign to character',
    example: 'random',
  })
  @IsString()
  colorId: string;

  @ApiProperty({
    description: 'Color type to assign to character',
    example: ColorType.REGULAR,
  })
  @IsEnum(ColorType)
  type: ColorType;
}
