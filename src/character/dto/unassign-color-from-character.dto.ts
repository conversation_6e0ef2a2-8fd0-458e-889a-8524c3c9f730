import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class UnassignColorFromCharacterDto {
  @ApiProperty({
    description: 'Character ID to assign color to',
    example: 'random',
  })
  @IsString()
  characterId: string;

  @ApiProperty({
    description: 'Color ID to assign to character',
    example: 'random',
  })
  @IsString()
  colorId: string;
}
